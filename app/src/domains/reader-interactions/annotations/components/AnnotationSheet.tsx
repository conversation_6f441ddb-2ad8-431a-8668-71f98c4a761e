/**
 * AnnotationSheet Component
 * Modern, site uyumlu annotation oluşturma sheet'i
 */
import React, { useState, useEffect } from 'react';
import { X, MessageSquare, Highlighter, Bookmark, FileText } from 'lucide-react';
import { autoOverlay } from '@shared/hooks/autooverlay';
import type { AnnotationType, CreateAnnotationInput } from '../../shared/types';

interface AnnotationSheetProps {
  isOpen: boolean;
  selectedText: string;
  annotationType: AnnotationType;
  onClose: () => void;
  onSubmit: (input: CreateAnnotationInput) => Promise<void>;
  isLoading?: boolean;
}

export function AnnotationSheet({
  isOpen,
  selectedText,
  annotationType,
  onClose,
  onSubmit,
  isLoading = false
}: AnnotationSheetProps) {
  const [content, setContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Styling
  const modalBgColor = autoOverlay ? autoOverlay(8, 'var(--bg-color)') : 'var(--bg-color)';
  const borderColor = autoOverlay ? autoOverlay(15, 'var(--bg-color)') : 'rgba(0,0,0,0.1)';
  const inputBgColor = autoOverlay ? autoOverlay(4, 'var(--bg-color)') : 'var(--bg-color)';
  const buttonBgColor = autoOverlay ? autoOverlay(5, 'var(--text-color)') : 'var(--text-color)';
  const buttonTextColor = autoOverlay ? autoOverlay(5, 'var(--bg-color)') : 'var(--bg-color)';

  // Type config
  const getTypeConfig = () => {
    switch (annotationType) {
      case 'note':
        return {
          icon: FileText,
          title: 'Not Ekle',
          color: '#3b82f6',
          placeholder: 'Notunuzu yazın...'
        };
      case 'sherh':
        return {
          icon: MessageSquare,
          title: 'Şerh Ekle',
          color: '#8b5cf6',
          placeholder: 'Şerhinizi yazın...'
        };
      case 'highlight':
        return {
          icon: Highlighter,
          title: 'Vurgula',
          color: '#f59e0b',
          placeholder: 'Vurgu açıklaması (opsiyonel)...'
        };
      case 'bookmark':
        return {
          icon: Bookmark,
          title: 'Yer İmi Ekle',
          color: '#10b981',
          placeholder: 'Yer imi açıklaması (opsiyonel)...'
        };
      default:
        return {
          icon: MessageSquare,
          title: 'Annotation Ekle',
          color: '#6b7280',
          placeholder: 'İçeriğinizi yazın...'
        };
    }
  };

  const typeConfig = getTypeConfig();

  // Reset form when sheet opens
  useEffect(() => {
    if (isOpen) {
      setContent('');
      setIsSaving(false);
    }
  }, [isOpen]);

  // Handle submit
  const handleSubmit = async () => {
    if (isSaving || isLoading) {
      return;
    }

    // Note ve Sherh için content zorunlu
    if ((annotationType === 'note' || annotationType === 'sherh') && !content.trim()) {
      const typeName = annotationType === 'note' ? 'Not' : 'Şerh';
      alert(`${typeName} içeriği boş olamaz`);
      return;
    }

    setIsSaving(true);
    try {
      const input: Partial<CreateAnnotationInput> = {
        annotation_type: annotationType,
        annotation_content: content.trim() || undefined,
        color: typeConfig.color,
        tags: [],
        is_public: false,
        metadata: {}
      };

      await onSubmit(input as CreateAnnotationInput);
    } catch (error) {
      console.error('[AnnotationSheet] Submit error:', error);
      alert('Kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const IconComponent = typeConfig.icon;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        onClick={handleBackdropClick}
      />

      {/* Sheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[480px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{
          backgroundColor: modalBgColor,
          borderColor: borderColor
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />

        {/* Header */}
        <div className="mx-4 mt-4 mb-2 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div
              className="p-2 rounded-lg"
              style={{ backgroundColor: `${typeConfig.color}15` }}
            >
              <IconComponent size={18} style={{ color: typeConfig.color }} />
            </div>
            <h3 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
              {typeConfig.title}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="px-6 pt-2 pb-6 overflow-y-auto max-h-[calc(80vh-8rem)] md:max-h-[calc(70vh-8rem)]">
          {/* Selected Text Preview */}
          <div
            className="p-4 rounded-lg mb-4 border-l-4"
            style={{
              backgroundColor: inputBgColor,
              borderLeftColor: typeConfig.color
            }}
          >
            <p className="text-sm opacity-70 mb-2" style={{ color: 'var(--text-color)' }}>
              Seçilen Metin:
            </p>
            <p className="text-sm leading-relaxed" style={{ color: 'var(--text-color)' }}>
              "{selectedText.length > 200 ? selectedText.substring(0, 200) + '...' : selectedText}"
            </p>
          </div>

          {/* Content Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-color)' }}>
              {annotationType === 'note' ? 'Not İçeriği' : 
               annotationType === 'sherh' ? 'Şerh İçeriği' : 'İçerik'}
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={typeConfig.placeholder}
              rows={annotationType === 'sherh' ? 8 : 6}
              className="w-full px-4 py-3 rounded-lg border text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/30"
              style={{
                backgroundColor: inputBgColor,
                borderColor: borderColor,
                color: 'var(--text-color)'
              }}
              disabled={isSaving || isLoading}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              disabled={isSaving || isLoading}
              className="flex-1 px-4 py-3 rounded-xl text-sm font-medium transition-colors hover:bg-[var(--text-color)]/5 disabled:opacity-50"
              style={{
                backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                color: 'var(--text-color)'
              }}
            >
              İptal
            </button>
            <button
              onClick={handleSubmit}
              disabled={
                ((annotationType === 'note' || annotationType === 'sherh') && !content.trim()) || 
                isSaving || 
                isLoading
              }
              className="flex-1 py-3 px-4 rounded-xl font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                backgroundColor: buttonBgColor,
                color: buttonTextColor
              }}
            >
              {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
