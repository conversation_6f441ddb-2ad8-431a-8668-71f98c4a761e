/**
 * AnnotationSheet Component
 * Annotation oluşturma ve düzenleme için sheet component'i
 */
import React, { useState, useEffect, useRef } from 'react';
import { X, MessageSquare, Highlighter, Bookmark, Save } from 'lucide-react';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import type { AnnotationType, CreateAnnotationInput } from '../../shared/types';
import { useBottomSheet } from '../../shared/hooks/useBottomSheet';
import { ANNOTATION_COLORS } from '../../shared/constants/ui';

interface AnnotationSheetProps {
  isOpen: boolean;
  selectedText: string;
  annotationType: AnnotationType; // Hangi tip annotation açılacak
  onClose: () => void;
  onSubmit: (input: CreateAnnotationInput) => Promise<void>;
  isLoading?: boolean;
}

// <PERSON><PERSON>i sistemi kaldırıldı - sadece tags kullanılacak

export function AnnotationSheet({
  isOpen,
  selectedText,
  annotationType,
  onClose,
  onSubmit,
  isLoading = false
}: AnnotationSheetProps) {
  const sheetRef = useRef<HTMLDivElement>(null);
  const [content, setContent] = useState('');
  const [selectedColor, setSelectedColor] = useState<string>(ANNOTATION_COLORS[0].value);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [isPublic, setIsPublic] = useState(false);

  // Hooks
  useBottomSheet({
    isOpen,
    onClose,
    sheetRef,
    type: 'annotation'
  });

  // Reset form when sheet opens
  useEffect(() => {
    if (isOpen) {
      setContent('');
      setTags([]);
      setTagInput('');
      setSelectedColor(ANNOTATION_COLORS[0].value);
      setIsPublic(false);
    }
  }, [isOpen]);

  // ESC tuşu ile kapanma
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  // Body scroll'unu engelle
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [isOpen]);

  const handleAddTag = () => {
    const trimmedTag = tagInput.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async () => {
    if (isLoading) {
      return;
    }

    // Note ve Sherh için content zorunlu
    if ((annotationType === 'note' || annotationType === 'sherh') && !content.trim()) {
      const typeName = annotationType === 'note' ? 'Not' : 'Şerh';
      alert(`${typeName} içeriği boş olamaz`);
      return;
    }

    const input: Partial<CreateAnnotationInput> = {
      annotation_type: annotationType,
      annotation_content: content.trim() || undefined,
      color: selectedColor,
      tags,
      is_public: isPublic,
      metadata: {}
    };

    try {
      await onSubmit(input as CreateAnnotationInput);
      // Form başarılı olduğunda kapanacak - onClose() çağrısını kaldırdık
      // çünkü TextSelectionHandler'da zaten handleFormClose() çağrılıyor
    } catch (error) {
      console.error('[AnnotationBottomSheet] Submit error:', error);
      // Hata durumunda kullanıcıya bilgi ver
      alert('Şerh kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Sadece backdrop'a tıklanırsa kapat (target === currentTarget)
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div
        className="flex min-h-full items-end sm:items-center justify-center p-0 sm:p-4"
        onClick={handleBackdropClick}
      >
        {/* Backdrop */}
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300" />

        {/* Modal */}
        <div
          className="relative w-full sm:max-w-lg bg-white rounded-t-2xl sm:rounded-xl shadow-2xl transform transition-all duration-300"
          onClick={(e) => e.stopPropagation()}
        >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            {annotationType === 'note' && <MessageSquare size={20} className="text-blue-600" />}
            {annotationType === 'sherh' && <MessageSquare size={20} className="text-purple-600" />}
            {annotationType === 'highlight' && <Highlighter size={20} className="text-yellow-600" />}
            {annotationType === 'bookmark' && <Bookmark size={20} className="text-green-600" />}
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {annotationType === 'note' && 'Not Ekle'}
                {annotationType === 'sherh' && 'Şerh Ekle'}
                {annotationType === 'highlight' && 'Vurgulama Ekle'}
                {annotationType === 'bookmark' && 'Yer İmi Ekle'}
              </h2>
              <p className="text-sm text-gray-500">
                Seçtiğiniz metne {
                  annotationType === 'note' ? 'not' :
                  annotationType === 'sherh' ? 'şerh' :
                  annotationType === 'highlight' ? 'vurgulama' : 'yer imi'
                } ekleyin
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div>
          {/* Selected Text Preview */}
          <div className="p-3 bg-gray-50 border-b border-gray-200">
            <p className="text-sm text-gray-600 mb-2">Seçilen Metin:</p>
            <div className="bg-white p-3 rounded-lg border-l-4 border-blue-400 text-sm">
              "{selectedText.length > 200 ? selectedText.substring(0, 200) + '...' : selectedText}"
            </div>
          </div>

          {/* Tab navigation kaldırıldı - direkt annotation türüne göre form açılıyor */}

          {/* Form Content */}
          <div
            className="p-4 space-y-4 overflow-y-auto max-h-96 overscroll-contain"
            style={{
              WebkitOverflowScrolling: 'touch',
              touchAction: 'pan-y'
            }}
          >
            {/* Content Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {annotationType === 'note' ? 'Şerh İçeriği *' : 'Açıklama (Opsiyonel)'}
              </label>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={
                  annotationType === 'note'
                    ? 'Şerhinizi yazın...'
                    : `${annotationType === 'highlight' ? 'Vurgulama' : 'Yer imi'} için opsiyonel açıklama...`
                }
                className="w-full h-24 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                autoFocus={annotationType === 'note'}
              />
              {annotationType === 'note' && !content.trim() && (
                <p className="text-xs text-red-500 mt-1">* Şerh içeriği zorunludur</p>
              )}
            </div>

            {/* Color Picker for Highlights */}
            {annotationType === 'highlight' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Renk</label>
                <div className="flex space-x-2">
                  {ANNOTATION_COLORS.map((color) => (
                    <button
                      key={color.value}
                      onClick={() => setSelectedColor(color.value)}
                      className={`w-8 h-8 rounded-full ${color.bg} border-2 transition-all ${
                        selectedColor === color.value
                          ? 'border-gray-800 scale-110'
                          : 'border-gray-300 hover:scale-105'
                      }`}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Kategori sistemi kaldırıldı - sadece tags kullanılıyor */}

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Etiketler</label>
              <div className="flex flex-wrap gap-2 mb-2">
                {tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {tag}
                    <button
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      <X size={14} />
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                  placeholder="Etiket ekle..."
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  onClick={handleAddTag}
                  disabled={!tagInput.trim()}
                  className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Ekle
                </button>
              </div>
            </div>

            {/* Public Checkbox */}
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Herkese açık yap</span>
            </label>
          </div>
        </div>

        {/* Footer */}
        <div className="flex space-x-3 p-4 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 py-3 px-4 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 transition-colors font-medium"
          >
            İptal
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || (annotationType === 'note' && !content.trim())}
            className="flex-2 flex items-center justify-center space-x-2 py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold shadow-md hover:shadow-lg"
          >
            <Save size={18} />
            <span className="text-base">
              {isLoading
                ? 'Kaydediliyor...'
                : `${annotationType === 'note' ? 'Şerhi Kaydet' : annotationType === 'highlight' ? 'Vurgulamayı Kaydet' : 'Yer İmini Kaydet'}`
              }
            </span>
          </button>
        </div>
        </div>
      </div>
    </div>
  );
}
