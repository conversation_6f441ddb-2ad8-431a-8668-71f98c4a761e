import React, { useState, useEffect } from 'react';
import { X, FileText } from 'lucide-react';
import { autoOverlay } from '@shared/hooks/autooverlay';

interface NoteBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  selectedText: string;
  noteContent: string;
  onNoteContentChange: (content: string) => void;
  onSave: () => void;
  loading?: boolean;
}

export const NoteBottomSheet: React.FC<NoteBottomSheetProps> = ({
  isOpen,
  onClose,
  selectedText,
  noteContent,
  onNoteContentChange,
  onSave,
  loading = false
}) => {
  const [isSaving, setIsSaving] = useState(false);

  // Styling
  const modalBgColor = autoOverlay ? autoOverlay(8, 'var(--bg-color)') : '#1e1e1e';
  const borderColor = autoOverlay ? autoOverlay(15, 'var(--bg-color)') : 'rgba(255,255,255,0.1)';
  const inputBgColor = autoOverlay ? autoOverlay(4, 'var(--bg-color)') : 'var(--bg-color)';
  const buttonBgColor = autoOverlay ? autoOverlay(5, 'var(--text-color)') : 'var(--text-color)';
  const buttonTextColor = autoOverlay ? autoOverlay(5, 'var(--bg-color)') : 'var(--bg-color)';

  // Reset form when sheet closes
  useEffect(() => {
    if (!isOpen) {
      setIsSaving(false);
    }
  }, [isOpen]);

  // Handle save
  const handleSave = async () => {
    if (!noteContent.trim()) {
      return;
    }
    
    setIsSaving(true);
    try {
      await onSave();
    } finally {
      setIsSaving(false);
    }
  };

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        onClick={handleBackdropClick}
      />

      {/* Sheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[480px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{
          backgroundColor: modalBgColor,
          borderColor: borderColor
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />

        {/* Header */}
        <div className="mx-4 mt-4 mb-2 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div
              className="p-2 rounded-lg"
              style={{ backgroundColor: '#3b82f615' }}
            >
              <FileText size={18} style={{ color: '#3b82f6' }} />
            </div>
            <h3 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
              Not Ekle
            </h3>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="px-6 pt-2 pb-6 overflow-y-auto max-h-[calc(80vh-8rem)] md:max-h-[calc(70vh-8rem)]">
          {/* Selected Text Preview */}
          <div
            className="p-4 rounded-lg mb-4 border-l-4"
            style={{
              backgroundColor: inputBgColor,
              borderLeftColor: '#3b82f6'
            }}
          >
            <p className="text-sm opacity-70 mb-2" style={{ color: 'var(--text-color)' }}>
              Seçilen Ayet:
            </p>
            <p className="text-sm leading-relaxed" style={{ color: 'var(--text-color)' }}>
              "{selectedText}"
            </p>
          </div>

          {/* Note Content */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-color)' }}>
              Not İçeriği
            </label>
            <textarea
              value={noteContent}
              onChange={(e) => onNoteContentChange(e.target.value)}
              placeholder="Bu ayet hakkında notunuzu yazın..."
              rows={6}
              className="w-full px-4 py-3 rounded-lg border text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/30"
              style={{
                backgroundColor: inputBgColor,
                borderColor: borderColor,
                color: 'var(--text-color)'
              }}
              disabled={isSaving || loading}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              disabled={isSaving || loading}
              className="flex-1 px-4 py-3 rounded-xl text-sm font-medium transition-colors hover:bg-[var(--text-color)]/5 disabled:opacity-50"
              style={{
                backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                color: 'var(--text-color)'
              }}
            >
              İptal
            </button>
            <button
              onClick={handleSave}
              disabled={!noteContent.trim() || isSaving || loading}
              className="flex-1 py-3 px-4 rounded-xl font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                backgroundColor: buttonBgColor,
                color: buttonTextColor
              }}
            >
              {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
