import React, { useState, useEffect } from 'react';
import { Co<PERSON>, Share2, MessageSquare, Highlighter, Bookmark, Search, FileText } from 'lucide-react';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import ColorPicker from '../../highlights/components/ColorPicker';
import { useReaderInteractionStyles } from '../../shared/hooks/useReaderInteractionStyles';

interface TextActionMenuProps {
  isVisible: boolean;
  position: { top: number; left: number; placement?: 'above' | 'below' } | null;
  menuRef?: React.RefObject<HTMLDivElement>;
  selectedText: string;
  onActionSelect: (actionType: 'copy' | 'share' | 'comment') => void;
  onCopy?: () => void; // Optional override for copy behavior
  // Annotation handlers - form açmak için
  onCreateNote?: () => void; // ✅ Not Al handler
  onCreateAnnotation?: () => void; // Şerh Ekle handler (mevcut)
  onCreateHighlight?: (color: string, style: 'background' | 'text') => void; // Renk ve stil parametresi eklendi
  onCreateBookmark?: () => void;
  onFindAnnotations?: () => void; // Şerh bul handler
  onClearAnnotations?: () => void; // Temizle handler
  // Seçim değiştiğinde color picker'ı kapatmak için key
  selectionKey?: string;
}

// ActionButton bileşeni
const ActionButton: React.FC<React.PropsWithChildren<{
  label: string;
  onClick?: () => void;
  hoverBgColor: string;
}>> = ({ children, label, onClick, hoverBgColor }) => {
  return (
    <button
      title={label}
      className="p-2 rounded text-[var(--text-color)]/80 hover:text-[var(--text-color)] transition-colors duration-150 flex items-center justify-center"
      onClick={onClick}
      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = hoverBgColor}
      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
    >
      {children}
    </button>
  );
};

export const TextActionMenu: React.FC<TextActionMenuProps> = ({
  isVisible,
  position,
  menuRef,
  selectedText,
  onActionSelect,
  onCopy,
  onCreateNote,
  onCreateAnnotation,
  onCreateHighlight,
  onCreateBookmark,
  onFindAnnotations,
  onClearAnnotations,
  selectionKey
}) => {
  // State for color picker
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#fbbf24'); // Default yellow
  const [selectedStyle, setSelectedStyle] = useState<'background' | 'text'>('background'); // Default background

  // Styles hook
  const { bgColors, getMenuStyles } = useReaderInteractionStyles();

  // Yeni seçim yapıldığında color picker'ı kapat
  useEffect(() => {
    setShowColorPicker(false);
  }, [selectionKey]);

  if (!isVisible || !position) {
    return null;
  }

  // Handle default copy function
  const handleCopy = () => {
    if (onCopy) {
      onCopy();
    } else {
      navigator.clipboard.writeText(selectedText || window.getSelection()?.toString() || '');
      onActionSelect('copy');
    }
  };

  // Handle highlight button click
  const handleHighlightClick = () => {
    if (!showColorPicker) {
      // ColorPicker açılırken seçim state'ini temizle
      setSelectedColor('');
      setSelectedStyle('background');
    }
    setShowColorPicker(!showColorPicker);
  };

  // Handle color selection
  const handleColorSelect = (color: string, style: 'background' | 'text') => {
    if (color === 'DELETE') {
      // Temizle işlemi
      setShowColorPicker(false);
      if (onClearAnnotations) {
        onClearAnnotations();
      }
    } else {
      // Normal renk seçimi
      setSelectedColor(color);
      setSelectedStyle(style);
      setShowColorPicker(false);

      if (onCreateHighlight) {
        onCreateHighlight(color, style);
      }
    }
  };



  // Get menu styles using hook
  const menuStyle = getMenuStyles(position, isVisible);

  return (
    <>
      <div
        ref={menuRef}
        className="flex items-center justify-between p-1 rounded-lg shadow-lg border relative"
        style={menuStyle}
      >
        {/* Sol taraf - Annotation butonları */}
        <div className="flex items-center">
          {/* ✅ Not Al */}
          {onCreateNote && (
            <ActionButton label="Not Al" onClick={onCreateNote} hoverBgColor={bgColors.hover}>
              <FileText size={16} />
            </ActionButton>
          )}

          {/* Şerh Ekle */}
          {onCreateAnnotation && (
            <ActionButton label="Şerh Ekle" onClick={onCreateAnnotation} hoverBgColor={bgColors.hover}>
              <MessageSquare size={16} />
            </ActionButton>
          )}

          {/* Vurgula */}
          {onCreateHighlight && (
            <ActionButton label="Vurgula" onClick={handleHighlightClick} hoverBgColor={bgColors.hover}>
              <Highlighter size={16} />
            </ActionButton>
          )}

          {/* Yer İmi */}
          {onCreateBookmark && (
            <ActionButton label="Yer İmi" onClick={onCreateBookmark} hoverBgColor={bgColors.hover}>
              <Bookmark size={16} />
            </ActionButton>
          )}
        </div>

        {/* Sağ taraf - Şerh Bul, Kopyala ve Paylaş */}
        <div className="flex items-center">
          {/* Şerh Bul */}
          {onFindAnnotations && (
            <ActionButton label="Şerh Bul" onClick={onFindAnnotations} hoverBgColor={bgColors.hover}>
              <Search size={16} />
            </ActionButton>
          )}

          {/* Kopyala */}
          <ActionButton label="Kopyala" onClick={handleCopy} hoverBgColor={bgColors.hover}>
            <Copy size={16} />
          </ActionButton>

          {/* Paylaş */}
          <ActionButton label="Paylaş" onClick={() => onActionSelect('share')} hoverBgColor={bgColors.hover}>
            <Share2 size={16} />
          </ActionButton>
        </div>

        {/* Renk Seçici - TextActionMenu içinde */}
        {showColorPicker && onCreateHighlight && position && (
          <div
            className="absolute z-50"
            style={{
              // Ekran konumuna göre dinamik açılım
              ...(window.innerHeight - position.top > 300 ? {
                // Aşağıda yeterli yer var - ColorPicker aşağı açılsın
                top: '100%',
                marginTop: '1px',
              } : {
                // Aşağıda yer yok - ColorPicker yukarı açılsın
                bottom: '100%',
                marginBottom: '1px',
              }),
              left: '50%',
              transform: 'translateX(-50%)',
            }}
          >
            <ColorPicker
              selectedColor={selectedColor}
              selectedStyle={selectedStyle}
              onColorSelect={handleColorSelect}
              compact={true}
              placement={window.innerHeight - position.top > 300 ? 'below' : 'above'}
            />
          </div>
        )}
      </div>
    </>
  );
};