import React, { useCallback, useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { useTextSelectionHandler } from '../hooks/useTextSelectionHandler';
import { useAnnotationManager } from '../../annotations/hooks/useAnnotationManager';
import { useCollectionManager } from '../../bookmarks/hooks/useCollectionManager';
import { useAuth } from '@domains/auth/hooks/useAuth';
import { TextActionMenu } from './TextActionMenu';
import { AnnotationSheet } from '../../annotations/components/AnnotationSheet';
import { AnnotationToast } from '../../shared/components/AnnotationToast';
import { AnnotationSearchSheet } from '../../annotations/components/AnnotationSearchSheet';
import { BookmarkBottomSheet } from '../../bookmarks/components/BookmarkBottomSheet';
import type { CreateAnnotationInput, BookmarkCollection, CreateCollectionInput } from '../../shared/types';

interface TextSelectionHandlerProps {
  children: React.ReactNode;
  className?: string;
  onAnnotationCreated?: () => void; // Yeni annotation oluşturulduğunda callback
}

/**
 * Text Selection Handler Component
 * Metin seçimi ve annotation oluşturma işlemlerini yöneten wrapper component
 */
export function TextSelectionHandler({
  children,
  className,
  onAnnotationCreated
}: TextSelectionHandlerProps) {
  const { bookId, sectionId } = useParams<{ bookId: string; sectionId: string }>();
  const { user } = useAuth();

  // Form state (Şerh için)
  const [isFormSheetOpen, setIsFormSheetOpen] = useState(false);
  const [formSelectedText, setFormSelectedText] = useState('');
  const [formSelectionData, setFormSelectionData] = useState<any>(null);

  // ✅ Note sheet state (Not için)
  const [isNoteSheetOpen, setIsNoteSheetOpen] = useState(false);
  const [noteSelectedText, setNoteSelectedText] = useState('');
  const [noteSelectionData, setNoteSelectionData] = useState<any>(null);

  // Toast state
  const [toastState, setToastState] = useState({
    isVisible: false,
    message: '',
    type: 'success' as 'success' | 'error'
  });

  // Search sheet state
  const [isSearchSheetOpen, setIsSearchSheetOpen] = useState(false);

  // Bookmark sheet state
  const [isBookmarkSheetOpen, setIsBookmarkSheetOpen] = useState(false);

  const showToast = useCallback((message: string, type: 'success' | 'error' = 'success') => {
    setToastState({ isVisible: true, message, type });
  }, []);

  const hideToast = useCallback(() => {
    setToastState(prev => ({ ...prev, isVisible: false }));
  }, []);

  const {
    selection,
    isSelecting,
    selectionRect,
    clearSelection,
    prepareAnnotationDataWithSelection,
    clearAnnotations, // 🗑️ Yeni silme fonksiyonu
    setIsFormOpen
  } = useTextSelectionHandler();

  // Sheet açık olduğunda form state'ini güncelle (selection koruması için)
  useEffect(() => {
    setIsFormOpen(isFormSheetOpen || isNoteSheetOpen || isSearchSheetOpen || isBookmarkSheetOpen);
  }, [isFormSheetOpen, isNoteSheetOpen, isSearchSheetOpen, isBookmarkSheetOpen, setIsFormOpen]);

  /**
   * Şerh arama handler'ı
   */
  const handleFindAnnotations = useCallback(() => {
    if (!selection?.text) {
      showToast('Arama için metin seçmelisiniz.', 'error');
      return;
    }

    setIsSearchSheetOpen(true);
  }, [selection, showToast]);

  /**
   * "Tümünü Gör" handler'ı - yeni sayfaya yönlendir
   */
  const handleViewAllAnnotations = useCallback((searchParams: {
    selectedText: string;
    sentenceIds: string[];
    bookId?: string;
    sectionId?: string;
  }) => {
    // URL parametrelerini oluştur
    const params = new URLSearchParams({
      text: searchParams.selectedText,
      sentences: searchParams.sentenceIds.join(','),
      ...(searchParams.bookId && { book: searchParams.bookId }),
      ...(searchParams.sectionId && { section: searchParams.sectionId })
    });

    // Yeni sayfaya yönlendir
    const url = `/annotations/search?${params.toString()}`;
    window.open(url, '_blank'); // Yeni tab'da aç

    // Bottom sheet'i kapat
    setIsSearchSheetOpen(false);
  }, []);

  const {
    annotations,
    createAnnotation,
    smartDeleteAnnotationsBySelection,
    deleteAnnotation,
    loading: annotationLoading
  } = useAnnotationManager();

  const {
    collections,
    createCollection,
    loading: collectionLoading
  } = useCollectionManager();

  /**
   * Basit ve etkili kısmi temizleme
   */
  const handleSmartClear = useCallback(async (
    bookId: string,
    sectionId: string,
    selection: { start: number; end: number; text: string; sentence_id: string | string[] }
  ) => {
    console.log('[handleSmartClear] Başlıyor:', {
      bookId,
      sectionId,
      selection: { start: selection.start, end: selection.end, text: selection.text }
    });

    // Mevcut annotation'ları al (zaten yüklü olan)
    const sentenceIds = Array.isArray(selection.sentence_id) ? selection.sentence_id : [selection.sentence_id];

    // Tüm annotation'ları filtrele
    const allRelevantAnnotations = annotations.filter(annotation => {
      // Bu sentence'a ait mi?
      const annotationSentenceIds = Array.isArray(annotation.sentence_id)
        ? annotation.sentence_id
        : [annotation.sentence_id];

      const hasSentenceMatch = sentenceIds.some(id => annotationSentenceIds.includes(id));

      // Highlight annotation'ı mı?
      const isHighlight = annotation.annotation_type === 'highlight';

      // Pozisyon kesişimi var mı? (hem kısmi hem tam kapsama)
      const hasOverlap = selection.start < annotation.selection_end &&
                        selection.end > annotation.selection_start;

      // TAM KAPSAMA: Seçim annotation'ı tamamen kapsıyor mu?
      const isFullyCovered = selection.start <= annotation.selection_start &&
                            selection.end >= annotation.selection_end;

      return hasSentenceMatch && isHighlight && (hasOverlap || isFullyCovered);
    });

    console.log('[handleSmartClear] Tüm kesişen annotations:', {
      count: allRelevantAnnotations.length,
      annotations: allRelevantAnnotations.map(a => ({
        id: a.id,
        start: a.selection_start,
        end: a.selection_end,
        text: a.selected_text,
        color: a.color,
        style: a.highlight_style
      }))
    });

    // Renk gruplarına ayır - en üstteki (son eklenen) annotation'ı bul
    const annotationsByColor = new Map();

    allRelevantAnnotations.forEach(annotation => {
      const colorKey = `${annotation.color}_${annotation.highlight_style || 'background'}`;
      if (!annotationsByColor.has(colorKey)) {
        annotationsByColor.set(colorKey, []);
      }
      annotationsByColor.get(colorKey).push(annotation);
    });

    console.log('[handleSmartClear] Renk grupları:', {
      groups: Array.from(annotationsByColor.entries()).map(([colorKey, anns]) => ({
        colorKey,
        count: anns.length,
        annotations: anns.map((a: any) => ({
          id: a.id,
          start: a.selection_start,
          end: a.selection_end,
          created_at: a.created_at
        }))
      }))
    });

    // Her renk grubu için en son eklenen annotation'ı al (üstte olan)
    const topAnnotations: any[] = [];
    for (const [, colorAnnotations] of annotationsByColor) {
      // En son eklenen (created_at en büyük olan)
      const latestAnnotation = colorAnnotations.reduce((latest: any, current: any) =>
        new Date(current.created_at) > new Date(latest.created_at) ? current : latest
      );
      topAnnotations.push(latestAnnotation);
    }

    console.log('[handleSmartClear] En üstteki annotations:', {
      count: topAnnotations.length,
      annotations: topAnnotations.map(a => ({
        id: a.id,
        start: a.selection_start,
        end: a.selection_end,
        text: a.selected_text,
        color: a.color,
        style: a.highlight_style,
        created_at: a.created_at
      }))
    });

    // Sadece en üstteki annotation'ları işle
    const relevantAnnotations = topAnnotations;

    console.log('[handleSmartClear] Bulunan annotations:', {
      count: relevantAnnotations.length,
      annotations: relevantAnnotations.map(a => ({
        id: a.id,
        start: a.selection_start,
        end: a.selection_end,
        text: a.selected_text
      }))
    });

    let deletedCount = 0;
    let modifiedCount = 0;

    // Önce tam kapsanan annotation'ları ayır
    const fullyCoveredAnnotations = [];
    const partiallyCoveredAnnotations = [];

    for (const annotation of relevantAnnotations) {
      const annotationStart = annotation.selection_start;
      const annotationEnd = annotation.selection_end;
      const selectionStart = selection.start;
      const selectionEnd = selection.end;

      // Tam kapsama kontrolü
      if (selectionStart <= annotationStart && selectionEnd >= annotationEnd) {
        fullyCoveredAnnotations.push(annotation);
      } else {
        // Kısmi kesişme
        partiallyCoveredAnnotations.push(annotation);
      }
    }

    console.log('[handleSmartClear] Kapsama analizi:', {
      fullyCovered: fullyCoveredAnnotations.length,
      partiallyCovered: partiallyCoveredAnnotations.length,
      fullyCoveredList: fullyCoveredAnnotations.map(a => ({
        id: a.id,
        start: a.selection_start,
        end: a.selection_end,
        text: a.selected_text.substring(0, 30) + '...',
        color: a.color
      }))
    });

    // 1. Önce tam kapsanan annotation'ları sil
    for (const annotation of fullyCoveredAnnotations) {
      console.log('[handleSmartClear] Tam kapsama - siliniyor:', {
        id: annotation.id,
        start: annotation.selection_start,
        end: annotation.selection_end,
        color: annotation.color
      });

      const success = await deleteAnnotation(annotation.id);
      if (success) {
        deletedCount++;
      }
    }

    // 2. Sonra kısmi kesişen annotation'ları işle
    for (const annotation of partiallyCoveredAnnotations) {
      const annotationStart = annotation.selection_start;
      const annotationEnd = annotation.selection_end;
      const selectionStart = selection.start;
      const selectionEnd = selection.end;

      console.log('[handleSmartClear] Kısmi kesişme - işleniyor:', {
        annotation: { start: annotationStart, end: annotationEnd, text: annotation.selected_text.substring(0, 30) + '...' },
        selection: { start: selectionStart, end: selectionEnd, text: selection.text.substring(0, 30) + '...' }
      });

      // Kısmi kesişme - annotation'ı böl
      console.log('[handleSmartClear] Kısmi kesişme - bölünüyor');

      // Orijinal annotation'ı sil
      const deleteSuccess = await deleteAnnotation(annotation.id);
      if (!deleteSuccess) {
        console.error('[handleSmartClear] Kısmi silme başarısız:', annotation.id);
        continue;
      }

      // Yeni annotation'lar oluştur
      const newAnnotations = [];

      // Sol kısım (seçimden önce)
      if (annotationStart < selectionStart) {
        const leftEnd = Math.min(selectionStart, annotationEnd);
        const leftText = annotation.selected_text.substring(0, leftEnd - annotationStart);

        if (leftText.trim()) {
          newAnnotations.push({
            book_id: annotation.book_id,
            section_id: annotation.section_id,
            sentence_id: annotation.sentence_id,
            annotation_type: annotation.annotation_type,
            selected_text: leftText.trim(),
            selection_start: annotationStart,
            selection_end: leftEnd,
            prefix_text: annotation.prefix_text,
            suffix_text: annotation.suffix_text,
            word_proximity: annotation.word_proximity,
            text_hash: annotation.text_hash,
            sentence_hash: annotation.sentence_hash,
            color: annotation.color,
            highlight_style: annotation.highlight_style,
            metadata: annotation.metadata,
            user_id: annotation.user_id
          });
        }
      }

      // Sağ kısım (seçimden sonra)
      if (annotationEnd > selectionEnd) {
        const rightStart = Math.max(selectionEnd, annotationStart);
        const rightStartOffset = rightStart - annotationStart;
        const rightText = annotation.selected_text.substring(rightStartOffset);

        if (rightText.trim()) {
          newAnnotations.push({
            book_id: annotation.book_id,
            section_id: annotation.section_id,
            sentence_id: annotation.sentence_id,
            annotation_type: annotation.annotation_type,
            selected_text: rightText.trim(),
            selection_start: rightStart,
            selection_end: annotationEnd,
            prefix_text: annotation.prefix_text,
            suffix_text: annotation.suffix_text,
            word_proximity: annotation.word_proximity,
            text_hash: annotation.text_hash,
            sentence_hash: annotation.sentence_hash,
            color: annotation.color,
            highlight_style: annotation.highlight_style,
            metadata: annotation.metadata,
            user_id: annotation.user_id
          });
        }
      }

      // Yeni annotation'ları ekle
      for (const newAnnotation of newAnnotations) {
        console.log('[handleSmartClear] Yeni annotation ekleniyor:', {
          start: newAnnotation.selection_start,
          end: newAnnotation.selection_end,
          text: newAnnotation.selected_text
        });

        const created = await createAnnotation(newAnnotation);
        if (created) {
          modifiedCount++;
        }
      }

      if (newAnnotations.length === 0) {
        // Hiç kısım kalmadı
        deletedCount++;
      }
    }

    console.log('[handleSmartClear] Sonuç:', {
      deletedCount,
      modifiedCount,
      fullyCoveredDeleted: fullyCoveredAnnotations.length,
      partiallyCoveredProcessed: partiallyCoveredAnnotations.length
    });
    return { deletedCount, modifiedCount };
  }, [annotations, deleteAnnotation, createAnnotation]);

  /**
   * ✅ Note sheet açma handler'ı (Not Al için)
   */
  const handleNoteOpen = useCallback(() => {
    if (!selection?.text || !user) {
      showToast('Not eklemek için metin seçmelisiniz ve giriş yapmalısınız.', 'error');
      return;
    }

    // Selection'ı state'e kaydet
    setNoteSelectedText(selection.text);
    setNoteSelectionData(selection);
    setIsNoteSheetOpen(true);
  }, [selection, user, showToast]);

  /**
   * Form açma handler'ı (Şerh Ekle için)
   */
  const handleFormOpen = useCallback(() => {
    if (!selection) {
      return;
    }

    // Selection'ı state'e kaydet
    setFormSelectedText(selection.text);
    setFormSelectionData(selection);
    setIsFormSheetOpen(true);
    setIsFormOpen(true);
  }, [selection, setIsFormOpen]);

  /**
   * Bookmark sheet açma handler'ı
   */
  const handleBookmarkOpen = useCallback(() => {
    if (!selection?.text || !user) {
      showToast('Yer imi eklemek için metin seçmelisiniz ve giriş yapmalısınız.', 'error');
      return;
    }

    setIsBookmarkSheetOpen(true);
  }, [selection, user, showToast]);

  /**
   * Bookmark sheet kapama handler'ı
   */
  const handleBookmarkClose = useCallback(() => {
    setIsBookmarkSheetOpen(false);
  }, []);

  /**
   * Koleksiyon seçimi handler'ı
   */
  const handleSelectCollection = useCallback(async (collection: BookmarkCollection) => {
    if (!selection?.text || !user || !bookId || !sectionId) {
      showToast('Yer imi eklemek için gerekli bilgiler eksik.', 'error');
      return;
    }

    try {
      // Annotation verilerini hazırla
      const annotationData = await prepareAnnotationDataWithSelection(bookId, sectionId, selection);
      if (!annotationData) {
        showToast('Annotation verisi hazırlanamadı.', 'error');
        return;
      }

      // Bookmark annotation'ı oluştur
      const bookmarkInput: CreateAnnotationInput = {
        ...annotationData,
        annotation_type: 'bookmark',
        collection_id: collection.id,
        user_id: user.id
      };

      const result = await createAnnotation(bookmarkInput);

      if (result) {
        showToast(`"${collection.name}" koleksiyonuna yer imi eklendi!`, 'success');
        setIsBookmarkSheetOpen(false);
        clearSelection();
        if (onAnnotationCreated) {
          onAnnotationCreated();
        }
      } else {
        showToast('Yer imi eklenirken hata oluştu.', 'error');
      }
    } catch (error) {
      console.error('Bookmark creation error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  }, [selection, user, bookId, sectionId, prepareAnnotationDataWithSelection, createAnnotation, showToast, clearSelection, onAnnotationCreated]);

  /**
   * Yeni koleksiyon oluşturma handler'ı
   */
  const handleCreateCollection = useCallback(async (input: Omit<CreateCollectionInput, 'user_id'>) => {
    if (!user) {
      showToast('Koleksiyon oluşturmak için giriş yapmalısınız.', 'error');
      return;
    }

    try {
      const result = await createCollection(input);
      if (result) {
        showToast(`"${result.name}" koleksiyonu oluşturuldu!`, 'success');
      } else {
        showToast('Koleksiyon oluşturulurken hata oluştu.', 'error');
      }
    } catch (error) {
      console.error('Collection creation error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  }, [user, createCollection, showToast]);

  /**
   * Vurgulama handler'ı - renk ve stil parametresi ile
   */
  const handleCreateHighlight = useCallback(async (color: string, style: 'background' | 'text') => {
    if (!selection?.text || !user) {
      showToast('Vurgulamak için metin seçmelisiniz ve giriş yapmalısınız.', 'error');
      return;
    }

    if (!bookId || !sectionId) {
      showToast('Kitap ve bölüm bilgisi bulunamadı.', 'error');
      return;
    }

    // Eğer "clear" seçildiyse, mevcut vurgulamaları akıllı şekilde temizle
    if (color === 'clear') {
      try {
        // Basit ve etkili kısmi temizleme
        const result = await handleSmartClear(bookId, sectionId, selection);

        const totalChanges = result.deletedCount + result.modifiedCount;
        if (totalChanges > 0) {
          let message = '';
          if (result.deletedCount > 0 && result.modifiedCount > 0) {
            message = `${result.deletedCount} vurgulama silindi, ${result.modifiedCount} vurgulama bölündü!`;
          } else if (result.deletedCount > 0) {
            message = `${result.deletedCount} vurgulama temizlendi!`;
          } else if (result.modifiedCount > 0) {
            message = `${result.modifiedCount} vurgulamadan seçilen kısım çıkarıldı!`;
          }

          showToast(message, 'success');
          if (onAnnotationCreated) {
            onAnnotationCreated(); // Annotation listesini yenile
          }
        } else {
          showToast('Bu metinde temizlenecek vurgulama bulunamadı.', 'success');
        }

        clearSelection();
        return;
      } catch (error) {
        console.error('Highlight clearing error:', error);
        showToast('Vurgulama temizlenirken hata oluştu.', 'error');
        return;
      }
    }

    try {
      // Annotation verilerini hazırla
      const annotationData = await prepareAnnotationDataWithSelection(bookId, sectionId, selection);
      if (!annotationData) {
        showToast('Annotation verisi hazırlanamadı.', 'error');
        return;
      }

      // Highlight annotation'ı oluştur
      const highlightInput: CreateAnnotationInput = {
        ...annotationData,
        annotation_type: 'highlight',
        color: color,
        highlight_style: style, // Artık veritabanında sütun var!
        user_id: user.id
      };

      const result = await createAnnotation(highlightInput);

      if (result) {
        showToast('Vurgulama başarıyla eklendi!', 'success');
        clearSelection(); // Seçimi temizle
        if (onAnnotationCreated) {
          onAnnotationCreated(); // Annotation oluşturuldu callback'i
        }
      } else {
        showToast('Vurgulama eklenirken hata oluştu.', 'error');
      }
    } catch (error) {
      console.error('Highlight creation error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  }, [selection, user, bookId, sectionId, prepareAnnotationDataWithSelection, createAnnotation, smartDeleteAnnotationsBySelection, showToast, clearSelection, onAnnotationCreated]);

  /**
   * 🗑️ Annotation temizleme handler'ı - YENİ BASİT SİSTEM
   */
  const handleClearAnnotations = useCallback(async () => {
    if (!selection?.text || !user) {
      showToast('Temizlemek için metin seçmelisiniz ve giriş yapmalısınız.', 'error');
      return;
    }

    try {
      // Yeni basit silme sistemi - seçilen alandaki TÜM annotation'ları sil
      const success = await clearAnnotations(deleteAnnotation);

      if (success) {
        showToast('Seçilen alandaki tüm annotation\'lar temizlendi!', 'success');
        if (onAnnotationCreated) {
          onAnnotationCreated(); // Annotation listesini yenile
        }
      } else {
        showToast('Bu alanda temizlenecek annotation bulunamadı.', 'success');
      }
    } catch (error) {
      console.error('Annotation clearing error:', error);
      showToast('Annotation temizlenirken hata oluştu.', 'error');
    }
  }, [selection, user, clearAnnotations, deleteAnnotation, showToast, onAnnotationCreated]);

  /**
   * Form kapama handler'ı
   */
  const handleFormClose = useCallback(() => {
    setFormSelectedText('');
    setFormSelectionData(null);
    setIsFormSheetOpen(false);
    setIsFormOpen(false);
  }, [setIsFormOpen]);

  /**
   * ✅ Note submit handler'ı (Not Al için)
   */
  const handleNoteSubmit = useCallback(async (input: CreateAnnotationInput) => {
    const currentSelection = noteSelectionData;

    if (!bookId || !sectionId || !currentSelection || !user) {
      if (!user) {
        showToast('Not eklemek için giriş yapmanız gerekiyor.', 'error');
      }
      return;
    }

    try {
      // Annotation data'sını hazırla
      const annotationData = await prepareAnnotationDataWithSelection(bookId, sectionId, currentSelection);

      if (!annotationData) {
        return;
      }

      // Input ile birleştir ve user_id ekle
      const completeInput: CreateAnnotationInput = {
        ...annotationData,
        ...input,
        user_id: user.id,
        annotation_type: 'note' // Not için sabit
      };

      // Annotation oluştur
      const result = await createAnnotation(completeInput);

      if (result) {
        showToast('Not başarıyla kaydedildi!');
        setIsNoteSheetOpen(false);
        clearSelection();
        if (onAnnotationCreated) {
          onAnnotationCreated();
        }
      } else {
        showToast('Not kaydedilemedi. Lütfen tekrar deneyin.', 'error');
      }
    } catch (error) {
      showToast('Not kaydedilirken bir hata oluştu.', 'error');
      throw error;
    }
  }, [bookId, sectionId, prepareAnnotationDataWithSelection, createAnnotation, clearSelection, showToast, noteSelectionData, user, onAnnotationCreated]);

  /**
   * Bottom sheet submit handler'ı (Şerh Ekle için)
   */
  const handleFormSubmit = useCallback(async (input: CreateAnnotationInput) => {
    const currentSelection = formSelectionData;

    if (!bookId || !sectionId || !currentSelection || !user) {
      if (!user) {
        showToast('Şerh eklemek için giriş yapmanız gerekiyor.', 'error');
      }
      return;
    }

    try {
      // Annotation data'sını hazırla - saved selection'ı kullan
      const annotationData = await prepareAnnotationDataWithSelection(bookId, sectionId, currentSelection);

      if (!annotationData) {
        return;
      }

      // Input ile birleştir ve user_id ekle
      const completeInput: CreateAnnotationInput = {
        ...annotationData,
        ...input,
        user_id: user.id, // User ID'yi ekle
        annotation_type: 'sherh' // Şerh için sabit
      };

      // Annotation oluştur
      const result = await createAnnotation(completeInput);

      if (result) {
        showToast(`${input.annotation_type === 'note' ? 'Şerh' : input.annotation_type === 'highlight' ? 'Vurgulama' : 'Yer imi'} başarıyla kaydedildi!`);
        handleFormClose();
        clearSelection();
        // Annotation oluşturuldu callback'ini çağır - gerçek zamanlı güncelleme için
        if (onAnnotationCreated) {
          onAnnotationCreated();
        }
      } else {
        showToast('Şerh kaydedilemedi. Lütfen tekrar deneyin.', 'error');
      }
    } catch (error) {
      showToast('Şerh kaydedilirken bir hata oluştu.', 'error');
      throw error;
    }
  }, [bookId, sectionId, prepareAnnotationDataWithSelection, createAnnotation, handleFormClose, clearSelection, showToast, formSelectionData, user]);

  /**
   * Menu pozisyonunu hesaplar
   */
  const getMenuPosition = useCallback(() => {
    if (!selectionRect) return null;

    // Viewport boyutlarını al
    const viewportHeight = window.innerHeight;
    const menuHeight = 45; // Daha doğru menu yüksekliği
    const padding = 2; // Çok yakın - daha da azaltıldı

    // Seçimin üstünde yer var mı kontrol et
    const spaceAbove = selectionRect.top;
    const spaceBelow = viewportHeight - selectionRect.bottom;

    let top: number;
    let placement: 'above' | 'below';

    if (spaceAbove >= menuHeight + padding) {
      // Üstte yer var, üstte göster - çok yakın
      top = selectionRect.top - menuHeight - padding;
      placement = 'above';
    } else if (spaceBelow >= menuHeight + padding) {
      // Altta yer var, altta göster - çok yakın
      top = selectionRect.bottom + padding;
      placement = 'below';
    } else {
      // Her iki tarafta da yer yok, daha fazla yer olan tarafta göster
      if (spaceAbove > spaceBelow) {
        top = Math.max(3, selectionRect.top - menuHeight - 1); // Minimum 3px üstten, çok yakın
        placement = 'above';
      } else {
        top = selectionRect.bottom + 1; // Neredeyse yapışık
        placement = 'below';
      }
    }

    return {
      top: top + window.scrollY, // Scroll offset'i ekle
      left: selectionRect.left + (selectionRect.width / 2) + window.scrollX,
      placement
    };
  }, [selectionRect]);

  return (
    <div className={className}>
      {children}

      {/* Text Action Menu - sadece sheet'ler açık değilse göster */}
      {!isFormSheetOpen && !isNoteSheetOpen && (
        <TextActionMenu
          isVisible={isSelecting && !!selection}
          position={getMenuPosition()}
          selectedText={selection?.text || ''}
          selectionKey={selection ? `${selection.start}-${selection.end}-${selection.text.length}` : undefined}
          onActionSelect={(action) => {
            if (action === 'copy') {
              navigator.clipboard.writeText(selection?.text || '');
            } else if (action === 'share') {
              // Share logic
            } else if (action === 'comment') {
              // Comment logic
            }
          }}
          onCreateNote={handleNoteOpen}
          onCreateAnnotation={handleFormOpen}
          onCreateHighlight={handleCreateHighlight}
          onCreateBookmark={handleBookmarkOpen}
          onFindAnnotations={handleFindAnnotations}
          onClearAnnotations={handleClearAnnotations}
        />
      )}

      {/* ✅ Note Sheet - Not Al için */}
      <AnnotationSheet
        isOpen={isNoteSheetOpen}
        selectedText={noteSelectedText}
        annotationType="note"
        onClose={() => setIsNoteSheetOpen(false)}
        onSubmit={handleNoteSubmit}
        isLoading={annotationLoading}
      />

      {/* Annotation Sheet - Şerh Ekle için */}
      <AnnotationSheet
        isOpen={isFormSheetOpen}
        selectedText={formSelectedText}
        annotationType="sherh"
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        isLoading={annotationLoading}
      />

      {/* Annotation Search Sheet */}
      <AnnotationSearchSheet
        isOpen={isSearchSheetOpen}
        onClose={() => setIsSearchSheetOpen(false)}
        selectedText={selection?.text || ''}
        sentenceIds={Array.isArray(selection?.sentence_id) ? selection.sentence_id : (selection?.sentence_id ? [selection.sentence_id] : [])}
        bookId={bookId}
        sectionId={sectionId}
        onViewAll={handleViewAllAnnotations}
      />

      {/* Bookmark Bottom Sheet */}
      <BookmarkBottomSheet
        isOpen={isBookmarkSheetOpen}
        onClose={handleBookmarkClose}
        selectedText={selection?.text || ''}
        collections={collections}
        onSelectCollection={handleSelectCollection}
        onCreateCollection={handleCreateCollection}
        loading={collectionLoading}
      />

      {/* Toast Notification */}
      <AnnotationToast
        isVisible={toastState.isVisible}
        message={toastState.message}
        type={toastState.type}
        onClose={hideToast}
      />


    </div>
  );
}
