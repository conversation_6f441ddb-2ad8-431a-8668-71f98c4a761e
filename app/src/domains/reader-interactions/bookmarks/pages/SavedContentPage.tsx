import { useEffect, useState } from 'react';
import { PageLayout, SavedContentCard, LoadingState, SearchBar } from '@shared/components';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useIsMobile } from '@shared/hooks/useIsMobile';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { savedContentService } from '../services/savedContentService';
import { Annotation } from '../../shared/types';
import { BarChart3, MessageSquare, Highlighter, Bookmark, Filter, Calendar, BookOpen } from 'lucide-react';
import { usePageLayout } from '@reader/hooks';
import { useReaderInteractionStyles } from '../../shared/hooks/useReaderInteractionStyles';
import { autoOverlay } from '@shared/hooks/autooverlay';

const SavedContentPage = () => {
  const isMobile = useIsMobile();
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const { bgColors, borderColors } = useReaderInteractionStyles();

  // State
  const [loading, setLoading] = useState(true);
  const [savedContent, setSavedContent] = useState<{
    notes: Annotation[];
    highlights: Annotation[];
    bookmarks: Annotation[];
  }>({
    notes: [],
    highlights: [],
    bookmarks: []
  });
  const [stats, setStats] = useState({
    total_count: 0,
    by_type: { note: 0, highlight: 0, bookmark: 0 },
    recent_count: 0
  });

  // ✅ Yeni state'ler - modern tasarım için
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState<'all' | 'note' | 'highlight' | 'bookmark'>('all');
  const [sortBy, setSortBy] = useState<'recent' | 'oldest' | 'alphabetical'>('recent');

  // Fetch saved content
  useEffect(() => {
    const fetchSavedContent = async () => {
      if (!user || !isAuthenticated) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Paralel olarak tüm tipleri getir
        const [notesResult, highlightsResult, bookmarksResult, statsResult] = await Promise.all([
          savedContentService.getSavedContentByType(user.id, 'note'),
          savedContentService.getSavedContentByType(user.id, 'highlight'),
          savedContentService.getSavedContentByType(user.id, 'bookmark'),
          savedContentService.getSavedContentStats(user.id)
        ]);

        setSavedContent({
          notes: notesResult,
          highlights: highlightsResult,
          bookmarks: bookmarksResult
        });

        if (statsResult) {
          setStats(statsResult);
        }
      } catch (error) {
        console.error('Failed to fetch saved content:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSavedContent();
  }, [user, isAuthenticated]);

  // Page title and layout
  const pageTitle = "Kaydettiğim İçerikler";
  const pageLayoutConfig = usePageLayout({
    title: pageTitle,
    isMobile
  });

  // ✅ Filtreleme ve sıralama fonksiyonları
  const getFilteredAndSortedContent = () => {
    let allContent: Annotation[] = [];

    // Aktif filtreye göre içerikleri birleştir
    if (activeFilter === 'all') {
      allContent = [...savedContent.notes, ...savedContent.highlights, ...savedContent.bookmarks];
    } else if (activeFilter === 'note') {
      allContent = savedContent.notes;
    } else if (activeFilter === 'highlight') {
      allContent = savedContent.highlights;
    } else if (activeFilter === 'bookmark') {
      allContent = savedContent.bookmarks;
    }

    // Arama terimine göre filtrele
    if (searchTerm) {
      allContent = allContent.filter(annotation =>
        annotation.selected_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (annotation.annotation_content && annotation.annotation_content.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Sıralama
    allContent.sort((a, b) => {
      if (sortBy === 'recent') {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      } else if (sortBy === 'oldest') {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      } else if (sortBy === 'alphabetical') {
        return a.selected_text.localeCompare(b.selected_text, 'tr');
      }
      return 0;
    });

    return allContent;
  };

  const filteredContent = getFilteredAndSortedContent();

  // Handle annotation click
  const handleAnnotationClick = (annotation: Annotation) => {
    console.log('Annotation clicked:', annotation);
    // TODO: Annotation detay modal'ı veya sayfasına yönlendir
  };

  // Loading state
  if (loading) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <div className="container mx-auto px-4 py-8 flex justify-center items-center min-h-[50vh]">
          <LoadingState message="Kaydettiğim içerikler yükleniyor..." />
        </div>
      </PageLayout>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <p className="text-lg" style={{ color: 'var(--text-color)' }}>
              Bu sayfayı görüntülemek için giriş yapmanız gerekiyor
            </p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // No saved content
  const totalItems = savedContent.notes.length + savedContent.highlights.length + savedContent.bookmarks.length;
  if (totalItems === 0) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <p className="text-lg mb-2" style={{ color: 'var(--text-color)' }}>
              Henüz kaydettiğiniz içerik yok
            </p>
            <p className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
              Metinleri seçerek şerh, vurgu ve yer imi ekleyebilirsiniz
            </p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // ✅ Modern Stats Cards - Ana sayfa tarzında
  const StatsCards = () => {
    const cardBgColor = autoOverlay ? autoOverlay(6, 'var(--bg-color)') : 'var(--bg-color)';
    const borderColor = autoOverlay ? autoOverlay(15, 'var(--bg-color)') : 'rgba(0,0,0,0.1)';

    const statCards = [
      {
        icon: MessageSquare,
        count: stats.by_type.note,
        label: 'Şerh',
        color: '#fbbf24',
        gradient: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
        filter: 'note' as const
      },
      {
        icon: Highlighter,
        count: stats.by_type.highlight,
        label: 'Vurgu',
        color: '#f59e0b',
        gradient: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        filter: 'highlight' as const
      },
      {
        icon: Bookmark,
        count: stats.by_type.bookmark,
        label: 'Yer İmi',
        color: '#10b981',
        gradient: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        filter: 'bookmark' as const
      },
      {
        icon: BarChart3,
        count: stats.total_count,
        label: 'Toplam',
        color: 'var(--text-color)',
        gradient: 'linear-gradient(135deg, var(--text-color) 0%, color-mix(in srgb, var(--text-color) 80%, transparent) 100%)',
        filter: 'all' as const
      }
    ];

    return (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {statCards.map((card) => {
          const IconComponent = card.icon;
          const isActive = activeFilter === card.filter;

          return (
            <div
              key={card.filter}
              onClick={() => setActiveFilter(card.filter)}
              className={`relative p-4 rounded-xl border cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg ${
                isActive ? 'ring-2 ring-opacity-50' : ''
              }`}
              style={{
                backgroundColor: cardBgColor,
                borderColor: isActive ? card.color : borderColor,
                ringColor: isActive ? card.color : 'transparent'
              }}
            >
              {/* Gradient accent */}
              <div
                className="absolute top-0 left-0 right-0 h-1 rounded-t-xl"
                style={{ background: card.gradient }}
              />

              <div className="flex items-center justify-between mb-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: `${card.color}15` }}
                >
                  <IconComponent
                    size={20}
                    style={{ color: card.color }}
                  />
                </div>
                {isActive && (
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: card.color }}
                  />
                )}
              </div>

              <div className="text-2xl font-bold mb-1" style={{ color: card.color }}>
                {card.count}
              </div>
              <div className="text-sm font-medium opacity-70" style={{ color: 'var(--text-color)' }}>
                {card.label}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // ✅ Filter ve Sort Controls
  const FilterControls = () => {
    const controlBgColor = autoOverlay ? autoOverlay(4, 'var(--bg-color)') : 'var(--bg-color)';
    const activeBgColor = autoOverlay ? autoOverlay(8, 'var(--bg-color)') : 'rgba(0,0,0,0.1)';

    return (
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        {/* Search Bar */}
        <div className="flex-1">
          <SearchBar
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="İçeriklerde ara..."
            className="w-full"
          />
        </div>

        {/* Sort Control */}
        <div className="flex items-center gap-2">
          <Calendar size={16} style={{ color: 'var(--text-color)' }} className="opacity-70" />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 rounded-lg border text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500/30"
            style={{
              backgroundColor: controlBgColor,
              borderColor: autoOverlay ? autoOverlay(15, 'var(--bg-color)') : 'rgba(0,0,0,0.1)',
              color: 'var(--text-color)'
            }}
          >
            <option value="recent">En Yeni</option>
            <option value="oldest">En Eski</option>
            <option value="alphabetical">Alfabetik</option>
          </select>
        </div>
      </div>
    );
  };

  return (
    <PageLayout {...pageLayoutConfig}>
      <main className="container mx-auto px-4 py-8 max-w-6xl">
        {/* ✅ Modern Stats Cards */}
        <StatsCards />

        {/* ✅ Filter ve Search Controls */}
        <FilterControls />

        {/* ✅ Content Grid - Unified Display */}
        {filteredContent.length > 0 ? (
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {filteredContent.map((annotation) => (
              <SavedContentCard
                key={annotation.id}
                annotation={annotation}
                onClick={handleAnnotationClick}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div
              className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
              style={{ backgroundColor: autoOverlay ? autoOverlay(8, 'var(--bg-color)') : 'rgba(0,0,0,0.05)' }}
            >
              {activeFilter === 'note' ? (
                <MessageSquare size={24} style={{ color: '#fbbf24' }} />
              ) : activeFilter === 'highlight' ? (
                <Highlighter size={24} style={{ color: '#f59e0b' }} />
              ) : activeFilter === 'bookmark' ? (
                <Bookmark size={24} style={{ color: '#10b981' }} />
              ) : (
                <BookOpen size={24} style={{ color: 'var(--text-color)' }} className="opacity-50" />
              )}
            </div>
            <p className="text-lg mb-2" style={{ color: 'var(--text-color)' }}>
              {searchTerm
                ? `"${searchTerm}" için sonuç bulunamadı`
                : activeFilter === 'all'
                  ? 'Henüz kaydettiğiniz içerik yok'
                  : `Henüz ${
                      activeFilter === 'note' ? 'şerh' :
                      activeFilter === 'highlight' ? 'vurgu' : 'yer imi'
                    } eklemediniz`
              }
            </p>
            <p className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
              {searchTerm
                ? 'Farklı anahtar kelimeler deneyin'
                : 'Metinleri seçerek şerh, vurgu ve yer imi ekleyebilirsiniz'
              }
            </p>
          </div>
        )}
      </main>
    </PageLayout>
  );
};

export default SavedContentPage;
