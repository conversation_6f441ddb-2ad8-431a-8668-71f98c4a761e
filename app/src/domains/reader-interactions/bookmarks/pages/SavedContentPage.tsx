import { useEffect, useState } from 'react';
import { PageLayout, SavedContentCard, LoadingState, SearchBar } from '@shared/components';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useIsMobile } from '@shared/hooks/useIsMobile';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { savedContentService } from '../services/savedContentService';
import { Annotation } from '../../shared/types';
import { BarChart3, MessageSquare, Highlighter, Bookmark, Filter, Calendar, BookOpen } from 'lucide-react';
import { usePageLayout } from '@reader/hooks';
import { useReaderInteractionStyles } from '../../shared/hooks/useReaderInteractionStyles';
import { autoOverlay } from '@shared/hooks/autooverlay';

const SavedContentPage = () => {
  const isMobile = useIsMobile();
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const { bgColors, borderColors } = useReaderInteractionStyles();

  // State
  const [loading, setLoading] = useState(true);
  const [savedContent, setSavedContent] = useState<{
    notes: Annotation[];
    highlights: Annotation[];
    bookmarks: Annotation[];
  }>({
    notes: [],
    highlights: [],
    bookmarks: []
  });
  const [stats, setStats] = useState({
    total_count: 0,
    by_type: { note: 0, highlight: 0, bookmark: 0 },
    recent_count: 0
  });

  // ✅ Yeni state'ler - modern tasarım için
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState<'all' | 'note' | 'highlight' | 'bookmark'>('all');
  const [sortBy, setSortBy] = useState<'recent' | 'oldest' | 'alphabetical'>('recent');

  // Fetch saved content
  useEffect(() => {
    const fetchSavedContent = async () => {
      if (!user || !isAuthenticated) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Paralel olarak tüm tipleri getir
        const [notesResult, highlightsResult, bookmarksResult, statsResult] = await Promise.all([
          savedContentService.getSavedContentByType(user.id, 'note'),
          savedContentService.getSavedContentByType(user.id, 'highlight'),
          savedContentService.getSavedContentByType(user.id, 'bookmark'),
          savedContentService.getSavedContentStats(user.id)
        ]);

        setSavedContent({
          notes: notesResult,
          highlights: highlightsResult,
          bookmarks: bookmarksResult
        });

        if (statsResult) {
          setStats(statsResult);
        }
      } catch (error) {
        console.error('Failed to fetch saved content:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSavedContent();
  }, [user, isAuthenticated]);

  // Page title and layout
  const pageTitle = "Kaydettiğim İçerikler";
  const pageLayoutConfig = usePageLayout({
    title: pageTitle,
    isMobile
  });

  // ✅ Filtreleme ve sıralama fonksiyonları
  const getFilteredAndSortedContent = () => {
    let allContent: Annotation[] = [];

    // Aktif filtreye göre içerikleri birleştir
    if (activeFilter === 'all') {
      allContent = [...savedContent.notes, ...savedContent.highlights, ...savedContent.bookmarks];
    } else if (activeFilter === 'note') {
      allContent = savedContent.notes;
    } else if (activeFilter === 'highlight') {
      allContent = savedContent.highlights;
    } else if (activeFilter === 'bookmark') {
      allContent = savedContent.bookmarks;
    }

    // Arama terimine göre filtrele
    if (searchTerm) {
      allContent = allContent.filter(annotation =>
        annotation.selected_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (annotation.annotation_content && annotation.annotation_content.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Sıralama
    allContent.sort((a, b) => {
      if (sortBy === 'recent') {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      } else if (sortBy === 'oldest') {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      } else if (sortBy === 'alphabetical') {
        return a.selected_text.localeCompare(b.selected_text, 'tr');
      }
      return 0;
    });

    return allContent;
  };

  const filteredContent = getFilteredAndSortedContent();

  // Handle annotation click
  const handleAnnotationClick = (annotation: Annotation) => {
    console.log('Annotation clicked:', annotation);
    // TODO: Annotation detay modal'ı veya sayfasına yönlendir
  };

  // Loading state
  if (loading) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <div className="container mx-auto px-4 py-8 flex justify-center items-center min-h-[50vh]">
          <LoadingState message="Kaydettiğim içerikler yükleniyor..." />
        </div>
      </PageLayout>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <p className="text-lg" style={{ color: 'var(--text-color)' }}>
              Bu sayfayı görüntülemek için giriş yapmanız gerekiyor
            </p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // No saved content
  const totalItems = savedContent.notes.length + savedContent.highlights.length + savedContent.bookmarks.length;
  if (totalItems === 0) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <p className="text-lg mb-2" style={{ color: 'var(--text-color)' }}>
              Henüz kaydettiğiniz içerik yok
            </p>
            <p className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
              Metinleri seçerek şerh, vurgu ve yer imi ekleyebilirsiniz
            </p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // ✅ Minimal Stats Bar - Daha sade ve küçük
  const StatsBar = () => {
    const bgColor = autoOverlay ? autoOverlay(4, 'var(--bg-color)') : 'var(--bg-color)';
    const borderColor = autoOverlay ? autoOverlay(10, 'var(--bg-color)') : 'rgba(0,0,0,0.1)';

    const filters = [
      { key: 'all', label: 'Tümü', count: stats.total_count, icon: BarChart3 },
      { key: 'note', label: 'Şerh', count: stats.by_type.note, icon: MessageSquare },
      { key: 'highlight', label: 'Vurgu', count: stats.by_type.highlight, icon: Highlighter },
      { key: 'bookmark', label: 'Yer İmi', count: stats.by_type.bookmark, icon: Bookmark }
    ];

    return (
      <div
        className="flex items-center justify-between p-3 rounded-lg border mb-6"
        style={{ backgroundColor: bgColor, borderColor }}
      >
        <div className="flex items-center space-x-6">
          {filters.map((filter) => {
            const IconComponent = filter.icon;
            const isActive = activeFilter === filter.key;

            return (
              <button
                key={filter.key}
                onClick={() => setActiveFilter(filter.key as any)}
                className={`flex items-center space-x-2 px-3 py-1.5 rounded-md transition-all duration-200 ${
                  isActive ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'
                }`}
              >
                <IconComponent
                  size={16}
                  style={{
                    color: isActive ? 'var(--text-color)' : 'color-mix(in srgb, var(--text-color) 70%, transparent)'
                  }}
                />
                <span
                  className={`text-sm font-medium ${isActive ? '' : 'opacity-70'}`}
                  style={{ color: 'var(--text-color)' }}
                >
                  {filter.label}
                </span>
                <span
                  className={`text-xs px-1.5 py-0.5 rounded-full ${isActive ? 'bg-[var(--text-color)]/15' : 'bg-[var(--text-color)]/10'}`}
                  style={{ color: 'var(--text-color)' }}
                >
                  {filter.count}
                </span>
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  // ✅ Minimal Search ve Sort
  const SearchAndSort = () => {
    return (
      <div className="flex flex-col sm:flex-row gap-3 mb-6">
        <div className="flex-1">
          <SearchBar
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder="İçeriklerde ara..."
            className="w-full"
          />
        </div>
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="px-3 py-2 rounded-lg border text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/30"
          style={{
            backgroundColor: autoOverlay ? autoOverlay(4, 'var(--bg-color)') : 'var(--bg-color)',
            borderColor: autoOverlay ? autoOverlay(15, 'var(--bg-color)') : 'rgba(0,0,0,0.1)',
            color: 'var(--text-color)'
          }}
        >
          <option value="recent">En Yeni</option>
          <option value="oldest">En Eski</option>
          <option value="alphabetical">Alfabetik</option>
        </select>
      </div>
    );
  };

  return (
    <PageLayout {...pageLayoutConfig}>
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* ✅ Minimal Stats Bar */}
        <StatsBar />

        {/* ✅ Search ve Sort */}
        <SearchAndSort />

        {/* ✅ Content Grid */}
        {filteredContent.length > 0 ? (
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
            {filteredContent.map((annotation) => (
              <SavedContentCard
                key={annotation.id}
                annotation={annotation}
                onClick={handleAnnotationClick}
                compact={true}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <p className="text-base mb-2 opacity-70" style={{ color: 'var(--text-color)' }}>
              {searchTerm
                ? `"${searchTerm}" için sonuç bulunamadı`
                : activeFilter === 'all'
                  ? 'Henüz kaydettiğiniz içerik yok'
                  : `Henüz ${
                      activeFilter === 'note' ? 'şerh' :
                      activeFilter === 'highlight' ? 'vurgu' : 'yer imi'
                    } eklemediniz`
              }
            </p>
            <p className="text-sm opacity-50" style={{ color: 'var(--text-color)' }}>
              {searchTerm
                ? 'Farklı anahtar kelimeler deneyin'
                : 'Metinleri seçerek şerh, vurgu ve yer imi ekleyebilirsiniz'
              }
            </p>
          </div>
        )}
      </main>
    </PageLayout>
  );
};

export default SavedContentPage;
