import React, { useState, useEffect } from 'react';
import { X, Bookmark } from 'lucide-react';
import { autoOverlay } from '@shared/hooks/autooverlay';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type { BookmarkCollection, CreateCollectionInput } from '../../shared/types';

// ✅ Meal seçimi için yeni tipler
interface MealOption {
  id: string;
  name: string;
  enabled: boolean;
}

interface BookmarkBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  selectedText: string;
  collections: BookmarkCollection[];
  onSelectCollection: (collection: BookmarkCollection, selectedMeals?: MealOption[]) => void;
  onCreateCollection: (input: CreateCollectionInput) => Promise<void>;
  loading?: boolean;
  // ✅ Quran ayetleri için meal seçimi
  availableTranslators?: Array<{ id: string; name: string }>;
  selectedTranslators?: string[];
  isQuranVerse?: boolean;
}

export const BookmarkBottomSheet: React.FC<BookmarkBottomSheetProps> = ({
  isOpen,
  onClose,
  selectedText,
  collections,
  onSelectCollection,
  onCreateCollection,
  loading = false,
  availableTranslators = [],
  selectedTranslators = [],
  isQuranVerse = false
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [newCollectionDescription, setNewCollectionDescription] = useState('');
  const [newCollectionColor, setNewCollectionColor] = useState('#3b82f6');
  const [newCollectionIcon, setNewCollectionIcon] = useState('bookmark');
  const [creating, setCreating] = useState(false);

  // ✅ Meal seçimi state'i
  const [mealOptions, setMealOptions] = useState<MealOption[]>([]);
  const [showMealSelection, setShowMealSelection] = useState(false);

  // ✅ Meal options'ları initialize et
  useEffect(() => {
    if (isQuranVerse && availableTranslators.length > 0) {
      const options: MealOption[] = [
        { id: 'arabic', name: 'Arapça Metin', enabled: true }, // Varsayılan olarak seçili
        ...availableTranslators.map(translator => ({
          id: translator.id,
          name: translator.name,
          enabled: selectedTranslators.includes(translator.id) // Mevcut seçili mealler
        }))
      ];
      setMealOptions(options);
    }
  }, [isQuranVerse, availableTranslators, selectedTranslators]);

  // Reset form when sheet closes
  useEffect(() => {
    if (!isOpen) {
      setShowCreateForm(false);
      setShowMealSelection(false);
      setNewCollectionName('');
      setNewCollectionDescription('');
      setNewCollectionColor('#3b82f6');
      setNewCollectionIcon('bookmark');
    }
  }, [isOpen]);

  // Handle collection creation
  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) return;

    setCreating(true);
    try {
      await onCreateCollection({
        name: newCollectionName.trim(),
        description: newCollectionDescription.trim() || undefined,
        color: newCollectionColor,
        icon: newCollectionIcon,
        user_id: '' // Will be set by the parent component
      });

      // Reset form
      setShowCreateForm(false);
      setNewCollectionName('');
      setNewCollectionDescription('');
    } catch (error) {
      console.error('Failed to create collection:', error);
    } finally {
      setCreating(false);
    }
  };

  // ✅ Meal seçimi toggle fonksiyonu
  const toggleMealOption = (mealId: string) => {
    setMealOptions(prev =>
      prev.map(option =>
        option.id === mealId
          ? { ...option, enabled: !option.enabled }
          : option
      )
    );
  };

  // ✅ Koleksiyon seçimi handler'ı - meal seçimi ile birlikte
  const handleCollectionSelect = (collection: BookmarkCollection) => {
    if (isQuranVerse && mealOptions.length > 0) {
      const selectedMeals = mealOptions.filter(option => option.enabled);
      onSelectCollection(collection, selectedMeals);
    } else {
      onSelectCollection(collection);
    }
  };



  const navBgColor = autoOverlay(8, 'var(--bg-color)');
  const toolbarBgColor = autoOverlay(4, 'var(--bg-color)');
  const buttonBgColor = autoOverlay(5, 'var(--text-color)');
  const buttonTextColor = autoOverlay(5, 'var(--bg-color)');

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        style={{ opacity: isOpen ? 1 : 0 }}
        onClick={onClose}
      />

      {/* Sheet - Similar styling to NavigationSheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[500px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{
          backgroundColor: navBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />

        {/* Header with Title and Close Button */}
        <div className="mx-4 mt-3 mb-2 flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-base font-medium text-[var(--text-color)]">
              Yer İmi Ekle
            </h3>
            <p className="text-sm opacity-70 mt-1" style={{ color: 'var(--text-color)' }}>
              "{selectedText.length > 50 ? selectedText.substring(0, 50) + '...' : selectedText}"
            </p>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)] ml-2"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content - Navigation sections */}
        <div className="px-4 pt-1 pb-6 overflow-y-auto max-h-[calc(80vh-8rem)] md:max-h-[calc(70vh-8rem)]">

          {!showCreateForm && !showMealSelection ? (
            <div className="space-y-2.5">
              {/* Collection List */}
              <div
                className="space-y-0 p-3 rounded-xl"
                style={{ backgroundColor: toolbarBgColor }}
              >
                <div className="mb-2">
                  <h4 className="text-xs font-medium uppercase opacity-50 px-2 py-1 text-[var(--text-color)]">
                    Koleksiyon Seçin
                  </h4>
                </div>

                {loading ? (
                  <div className="flex flex-col items-center justify-center py-8 text-sm opacity-70 text-[var(--text-color)]">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <p>Koleksiyonlar yükleniyor...</p>
                  </div>
                ) : collections.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 text-sm opacity-70 text-[var(--text-color)]">
                    <Bookmark size={32} className="mx-auto mb-2 opacity-50" />
                    <p>Henüz koleksiyonunuz yok</p>
                  </div>
                ) : (
                  collections.map((collection) => {
                    return (
                      <button
                        key={collection.id}
                        onClick={() => {
                          if (isQuranVerse) {
                            setShowMealSelection(true);
                            // Seçilen koleksiyonu geçici olarak sakla
                            (window as any).selectedCollection = collection;
                          } else {
                            handleCollectionSelect(collection);
                          }
                        }}
                        className="w-full px-4 py-3 text-left rounded-none text-sm flex items-center justify-between relative hover:bg-[var(--text-color)]/5 text-[var(--text-color)] border-b border-[color-mix(in_srgb,var(--text-color)_10%,transparent)] last:border-b-0"
                        style={{ color: 'var(--text-color)' }}
                      >
                        <div className="flex-1 text-left">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium break-words">
                              {collection.name}
                            </span>
                            {collection.is_default && (
                              <span
                                className="text-xs px-1.5 py-0.5 rounded-full"
                                style={{
                                  backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                                  color: 'var(--text-color)',
                                  opacity: 0.7
                                }}
                              >
                                Varsayılan
                              </span>
                            )}
                          </div>
                          {collection.description && (
                            <p className="text-xs opacity-70 mt-0.5">
                              {collection.description}
                            </p>
                          )}
                        </div>
                      </button>
                    );
                  })
                )}
              </div>

              {/* Create New Collection Button */}
              <button
                onClick={() => setShowCreateForm(true)}
                className="w-full py-3 px-4 rounded-xl font-medium transition-all"
                style={{
                  backgroundColor: buttonBgColor,
                  color: buttonTextColor
                }}
              >
                Yeni Koleksiyon Oluştur
              </button>
            </div>
          ) : showMealSelection ? (
            <div className="space-y-2.5">
              {/* ✅ Meal Selection */}
              <div
                className="space-y-4 p-3 rounded-xl"
                style={{ backgroundColor: toolbarBgColor }}
              >
                <div className="mb-2">
                  <h4 className="text-xs font-medium uppercase opacity-50 px-2 py-1 text-[var(--text-color)]">
                    Kaydetmek İstediğiniz İçerikleri Seçin
                  </h4>
                </div>

                {/* Meal Options */}
                <div className="space-y-2">
                  {mealOptions.map((option) => (
                    <label
                      key={option.id}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-[var(--text-color)]/5 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={option.enabled}
                        onChange={() => toggleMealOption(option.id)}
                        className="w-4 h-4 rounded border-2 border-[var(--text-color)]/20 text-blue-600 focus:ring-blue-500 focus:ring-2"
                      />
                      <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                        {option.name}
                      </span>
                    </label>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => setShowMealSelection(false)}
                    className="flex-1 px-4 py-2.5 rounded-xl text-sm font-medium transition-colors hover:bg-[var(--text-color)]/5"
                    style={{
                      backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                      color: 'var(--text-color)'
                    }}
                  >
                    Geri
                  </button>
                  <button
                    onClick={() => {
                      const selectedCollection = (window as any).selectedCollection;
                      if (selectedCollection) {
                        handleCollectionSelect(selectedCollection);
                      }
                    }}
                    disabled={!mealOptions.some(option => option.enabled)}
                    className="flex-1 py-3 px-4 rounded-xl font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{
                      backgroundColor: buttonBgColor,
                      color: buttonTextColor
                    }}
                  >
                    Kaydet
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-2.5">
              {/* Create Collection Form */}
              <div
                className="space-y-4 p-3 rounded-xl"
                style={{ backgroundColor: toolbarBgColor }}
              >
                <div className="mb-2">
                  <h4 className="text-xs font-medium uppercase opacity-50 px-2 py-1 text-[var(--text-color)]">
                    Yeni Koleksiyon
                  </h4>
                </div>

                {/* Collection Name */}
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-color)' }}>
                    Koleksiyon Adı *
                  </label>
                  <input
                    type="text"
                    value={newCollectionName}
                    onChange={(e) => setNewCollectionName(e.target.value)}
                    placeholder="Örn: Önemli Ayetler"
                    className="w-full px-4 py-2.5 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70 focus:outline-none"
                    style={{
                      backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)'
                    }}
                    maxLength={100}
                  />
                </div>

                {/* Collection Description */}
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-color)' }}>
                    Açıklama (İsteğe bağlı)
                  </label>
                  <textarea
                    value={newCollectionDescription}
                    onChange={(e) => setNewCollectionDescription(e.target.value)}
                    placeholder="Bu koleksiyonun amacını açıklayın..."
                    className="w-full px-4 py-2.5 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70 resize-none focus:outline-none"
                    style={{
                      backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)'
                    }}
                    rows={3}
                    maxLength={500}
                  />
                </div>



                {/* Form Actions */}
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => setShowCreateForm(false)}
                    className="flex-1 px-4 py-2.5 rounded-xl text-sm font-medium transition-colors hover:bg-[var(--text-color)]/5"
                    style={{
                      backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                      color: 'var(--text-color)'
                    }}
                  >
                    İptal
                  </button>
                  <button
                    onClick={handleCreateCollection}
                    disabled={!newCollectionName.trim() || creating}
                    className="flex-1 py-3 px-4 rounded-xl font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{
                      backgroundColor: buttonBgColor,
                      color: buttonTextColor
                    }}
                  >
                    {creating ? 'Oluşturuluyor...' : 'Oluştur'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};