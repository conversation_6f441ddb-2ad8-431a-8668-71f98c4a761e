import React, { useState, useEffect } from 'react';
import {
  Copy,
  Bookmark,
  Search,
  Volume2,
  BookOpen,
  X,
  FileText,
  Columns,
  Eye,
  Info,
  ScanText,
  Tag,
  Check,
} from 'lucide-react';
import { autoOverlay } from '@shared/hooks/autooverlay';
import { CombinedVerseData, ITranslator } from '@reader/models/types';
// ✅ Bookmark sistemi i<PERSON> gere<PERSON> imports
import { useAnnotationManager } from '@domains/reader-interactions/annotations/hooks/useAnnotationManager';
import { useCollectionManager } from '@domains/reader-interactions/bookmarks/hooks/useCollectionManager';
import { BookmarkBottomSheet } from '@domains/reader-interactions/bookmarks/components/BookmarkBottomSheet';
import { NoteBottomSheet } from '@domains/reader-interactions/annotations/components/NoteBottomSheet';
import { AnnotationToast } from '@domains/reader-interactions/shared/components/AnnotationToast';
import { useAuthStore } from '@domains/auth/store/authStore';
import type { CreateAnnotationInput } from '@domains/reader-interactions/shared/types';

interface UnifiedActionMenuProps {
  isOpen?: boolean;
  onClose?: () => void;
  verseKey?: string;
  verseData?: CombinedVerseData;
  availableTranslators?: ITranslator[];
  selectedTranslators?: string[];
  surahName?: string;
  selectionKey?: string;
  isMobile?: boolean;

  onCopy?: () => void;
  onCreateAnnotation?: () => void;
  onCreateBookmark?: () => void;
  onFindAnnotations?: () => void;
  onListen?: () => void;
  onViewTafsir?: () => void;
}

export const UnifiedActionMenu: React.FC<UnifiedActionMenuProps> = ({
  isOpen,
  onClose,
  verseKey,
  verseData,
  availableTranslators,
  selectedTranslators,
  surahName,
  selectionKey,
  isMobile,
  onCreateAnnotation,
  onCreateBookmark,
  onListen,
  onViewTafsir,
  onFindAnnotations,
}) => {
  const [isCopied, setIsCopied] = useState(false);

  // ✅ Bookmark sistemi için state ve hooks
  const [isBookmarkSheetOpen, setIsBookmarkSheetOpen] = useState(false);

  // ✅ Note sistemi için state
  const [isNoteSheetOpen, setIsNoteSheetOpen] = useState(false);
  const [noteContent, setNoteContent] = useState('');

  // ✅ Toast notification state
  const [toastState, setToastState] = useState({
    isVisible: false,
    message: '',
    type: 'success' as 'success' | 'error'
  });

  const { user } = useAuthStore();
  const { createAnnotation } = useAnnotationManager();
  const { collections, loadCollections, createCollection } = useCollectionManager();

  // ✅ Koleksiyonları yükle
  useEffect(() => {
    if (user) {
      loadCollections();
    }
  }, [user, loadCollections]);

  // ✅ Toast helper fonksiyonları
  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToastState({ isVisible: true, message, type });
  };

  const hideToast = () => {
    setToastState(prev => ({ ...prev, isVisible: false }));
  };

  // ✅ Unicode-safe hash fonksiyonu
  const createHash = (str: string): string => {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 32-bit integer'a çevir
    }
    return Math.abs(hash).toString(36); // Base36 string olarak döndür
  };

  useEffect(() => {
    setIsCopied(false);
  }, [selectionKey, isOpen]);

  const handleCopy = () => {
    const showCopiedMessage = () => {
      setIsCopied(true);
      setTimeout(() => {
        setIsCopied(false);
        onClose?.();
      }, 1500); 
    };

    if (!verseData) {
      console.error("No verseData available for copying.");
      return;
    }

    let textToCopy = `${verseData.arabic_text || ''}\\n\\n`;

    if (verseData.translations && availableTranslators && selectedTranslators) {
      const selectedTranslationsText = selectedTranslators.map(translatorId => {
        const translator = availableTranslators.find(t => String(t.id) === translatorId);
        const translationData = verseData.translations?.[translatorId];
        if (translator && translationData && translationData.paragraphs) {
          const translationText = translationData.paragraphs.join('\\n');
          return `[${translator.name}]\\n${translationText}`;
        }
        return '';
      }).filter(Boolean).join('\\n\\n');

      if (selectedTranslationsText) {
        textToCopy += `${selectedTranslationsText}\\n\\n`;
      }
    }

    textToCopy += `(${surahName || 'Sure'}, ${verseData.verse_no}. Ayet)`;

    navigator.clipboard.writeText(textToCopy).then(() => {
      showCopiedMessage();
    }).catch(err => {
      console.error("Failed to copy text to clipboard:", err);
    });
  };

  // ✅ Note handler - not ekleme
  const handleNoteClick = () => {
    if (!user) {
      showToast('Not eklemek için giriş yapmanız gerekiyor.', 'error');
      return;
    }

    if (!verseData || !verseKey) {
      console.error('Verse data or key missing for note');
      showToast('Ayet bilgileri eksik.', 'error');
      return;
    }

    setIsNoteSheetOpen(true);
  };

  // ✅ Bookmark handler - tüm ayeti kaydet
  const handleBookmarkClick = () => {
    if (!user) {
      showToast('Yer imi eklemek için giriş yapmanız gerekiyor.', 'error');
      return;
    }

    if (!verseData || !verseKey) {
      console.error('Verse data or key missing for bookmark');
      showToast('Ayet bilgileri eksik.', 'error');
      return;
    }

    setIsBookmarkSheetOpen(true);
  };

  // ✅ Koleksiyon seçildiğinde bookmark oluştur
  const handleBookmarkSave = async (collection: any, selectedMeals?: any[]) => {
    if (!user || !verseData || !verseKey) return;

    try {
      // ✅ Seçilen meallere göre ayet metnini hazırla
      let fullVerseText = '';
      const savedContentTypes: string[] = [];

      if (selectedMeals && selectedMeals.length > 0) {
        // Meal seçimi yapıldı - sadece seçilenleri ekle
        selectedMeals.forEach(meal => {
          if (meal.enabled) {
            if (meal.id === 'arabic') {
              // ✅ Arapça metin
              if (verseData.arabic_text) {
                fullVerseText += verseData.arabic_text + '\n\n';
                savedContentTypes.push('arabic');
              }
            } else {
              // Meal metni
              const translator = availableTranslators?.find(t => String(t.id) === meal.id);
              const translationData = verseData.translations?.[meal.id];
              if (translator && translationData && translationData.paragraphs) {
                const translationText = translationData.paragraphs.join('\n');
                fullVerseText += `[${translator.name}]\n${translationText}\n\n`;
                savedContentTypes.push(meal.id);
              }
            }
          }
        });
      } else {
        // Varsayılan: Arapça + mevcut seçili mealler
        fullVerseText = verseData.arabic_text || '';
        savedContentTypes.push('arabic');

        if (verseData.translations && availableTranslators && selectedTranslators) {
          const translationsText = selectedTranslators.map(translatorId => {
            const translator = availableTranslators.find(t => String(t.id) === translatorId);
            const translationData = verseData.translations?.[translatorId];
            if (translator && translationData && translationData.paragraphs) {
              const translationText = translationData.paragraphs.join('\n');
              savedContentTypes.push(translatorId);
              return `[${translator.name}]\n${translationText}`;
            }
            return '';
          }).filter(Boolean).join('\n\n');

          if (translationsText) {
            fullVerseText += `\n\n${translationsText}`;
          }
        }
      }

      // Son satırdaki fazla boşlukları temizle
      fullVerseText = fullVerseText.trim();

      // Bookmark annotation input'u hazırla
      const [surahId, verseNo] = verseKey.split('-');

      // ✅ Sentence ID'yi annotation service'in beklediği formata çevir
      // Format: "surah_verse_0" (örn: "1_1_0", "2_255_0")
      const sentenceId = `${surahId}_${verseNo}_0`;

      const bookmarkInput: CreateAnnotationInput = {
        user_id: user.id,
        book_id: 'quran', // Quran için sabit book_id
        section_id: surahId, // Sure ID'si section_id olarak
        sentence_id: [sentenceId], // Annotation service'in beklediği format
        selection_start: 0,
        selection_end: fullVerseText.length,
        selected_text: fullVerseText,
        prefix_text: '', // Ayet için prefix/suffix gerekli değil
        suffix_text: '',
        word_proximity: [],
        text_hash: createHash(fullVerseText), // Unicode-safe hash
        sentence_hash: createHash(verseKey),
        annotation_type: 'bookmark',
        collection_id: collection.id,
        color: '#10b981', // Yeşil renk bookmark için
        metadata: {
          surah_name: surahName,
          verse_number: verseNo,
          verse_key: verseKey,
          saved_content_types: savedContentTypes, // Hangi içerikler kaydedildi
          selected_meals: selectedMeals?.filter(m => m.enabled).map(m => ({ id: m.id, name: m.name })) || []
        }
      };

      const result = await createAnnotation(bookmarkInput);

      if (result) {
        console.log('Bookmark başarıyla kaydedildi:', result);
        setIsBookmarkSheetOpen(false);
        onClose?.(); // Menu'yu kapat

        // Success feedback
        showToast(`Ayet "${collection.name || 'koleksiyon'}"a kaydedildi!`, 'success');
      } else {
        showToast('Yer imi kaydedilirken hata oluştu.', 'error');
      }
    } catch (error) {
      console.error('Bookmark save error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  };

  // ✅ Note kaydetme handler'ı
  const handleNoteSave = async () => {
    if (!user || !verseData || !verseKey || !noteContent.trim()) {
      showToast('Not içeriği boş olamaz.', 'error');
      return;
    }

    try {
      // Ayet metnini hazırla (sadece Arapça)
      const fullVerseText = verseData.arabic_text || '';

      if (!fullVerseText) {
        showToast('Ayet metni bulunamadı.', 'error');
        return;
      }

      // Sentence ID'yi hazırla
      const [surahId, verseNo] = verseKey.split('-');
      const sentenceId = `${surahId}_${verseNo}_0`;

      const noteInput: CreateAnnotationInput = {
        user_id: user.id,
        book_id: 'quran',
        section_id: surahId,
        sentence_id: [sentenceId],
        selection_start: 0,
        selection_end: fullVerseText.length,
        selected_text: fullVerseText,
        prefix_text: '',
        suffix_text: '',
        word_proximity: [],
        text_hash: createHash(fullVerseText),
        sentence_hash: createHash(verseKey),
        annotation_type: 'note',
        annotation_content: noteContent.trim(),
        color: '#3b82f6', // Mavi renk note için
        metadata: {
          surah_name: surahName,
          verse_number: verseNo,
          verse_key: verseKey
        }
      };

      const result = await createAnnotation(noteInput);

      if (result) {
        console.log('Note başarıyla kaydedildi:', result);
        setIsNoteSheetOpen(false);
        setNoteContent(''); // Form'u temizle
        onClose?.(); // Menu'yu kapat
        showToast('Not başarıyla eklendi!', 'success');
      } else {
        showToast('Not kaydedilirken hata oluştu.', 'error');
      }
    } catch (error) {
      console.error('Note save error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  };

  // ✅ Yeni koleksiyon oluşturma handler'ı
  const handleCreateCollection = async (input: any) => {
    if (!user) return;

    try {
      await createCollection({
        ...input,
        user_id: user.id
      });

      // Koleksiyonları yeniden yükle
      await loadCollections();
    } catch (error) {
      console.error('Collection creation error:', error);
      throw error;
    }
  };

  const executeAction = (e: React.MouseEvent, action: Function | undefined) => {
    e.stopPropagation();
    if (typeof action === 'function') {
      action(e);
    }
  };

  if (!isOpen) return null;

  const menuItems = [
    { icon: Volume2, text: 'Dinle', action: 'listen', handler: onListen, show: false },
    isCopied
      ? { icon: Check, text: 'Kopyalandı!', action: 'copy', handler: undefined, show: true, isGreen: true }
      : { icon: Copy, text: 'Kopyala', action: 'copy', handler: handleCopy, show: true },
    { icon: BookOpen, text: 'Tefsire Git', action: 'goto_tafsir', handler: onViewTafsir, show: false },
    // ✅ Bookmark butonunu aktif hale getir
    { icon: Bookmark, text: 'Kaydet', action: 'add_bookmark', handler: handleBookmarkClick, show: true },
    // ✅ Note butonunu aktif hale getir
    { icon: FileText, text: 'Not Al', action: 'add_note', handler: handleNoteClick, show: true },
    { icon: Columns, text: 'Meal Karşılaştır', action: 'compare_translations', handler: () => console.log('Compare translations'), show: false },
    { icon: Eye, text: 'Tefsir Görüntüle', action: 'view_tafsir', handler: () => console.log('View tafsir'), show: false },
    { icon: Tag, text: 'Etiket', action: 'add_tag', handler: () => console.log('Add tag'), show: false },
    { icon: Info, text: 'Ayet Bilgisi', action: 'verse_info', handler: () => console.log('Verse info'), show: false },
    { icon: ScanText, text: 'Kelime Detayı', action: 'word_details', handler: () => console.log('Word details'), show: true },
    { icon: Search, text: 'Şerh Bul', action: 'find_annotations', handler: onFindAnnotations, show: false }
  ].filter(item => item.show);

  const sheetBgColor = autoOverlay(8, 'var(--bg-color)') as string;
  const [surahNum, verseNum] = verseKey?.split('-') || ['', ''];
  const sheetTitle = `${surahName || `Sure ${surahNum}`}, ${verseNum}. Ayet (${surahNum}:${verseNum})`;

  const backdropClasses = "fixed inset-0 bg-black/40 backdrop-blur-[1px] z-40 transition-opacity";
  
  const wrapperClasses = isMobile
    ? `fixed z-50 bottom-0 left-0 right-0 transition-transform duration-300 ease-in-out ${isOpen ? 'translate-y-0' : 'translate-y-full'}`
    : `fixed inset-0 z-50 flex items-center justify-center transition-opacity duration-300 ${isOpen ? 'opacity-100' : 'opacity-0'}`;

  const sheetContainerClasses = isMobile
    ? 'w-full rounded-t-2xl shadow-2xl border-t'
    : 'w-full max-w-md rounded-2xl shadow-2xl border';

  return (
    <>
      <div className={`${backdropClasses} ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={onClose} />
      <div className={wrapperClasses} style={{ pointerEvents: isOpen ? 'auto' : 'none' }} onClick={!isMobile ? onClose : undefined}>
        <div className={`${sheetContainerClasses} border-opacity-20`} style={{ backgroundColor: sheetBgColor, borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)' }} onClick={(e) => e.stopPropagation()}>
          <div className="md:hidden w-10 h-1 mx-auto mt-2 mb-0 rounded-full bg-[var(--text-color)] opacity-15" />
          <div className="flex justify-between items-center px-4 pt-1 pb-1 mb-1" style={{ backgroundColor: sheetBgColor }}>
            <span className="text-base font-medium" style={{ color: 'var(--text-color)' }}>{sheetTitle}</span>
            <button onClick={onClose} className="p-1.5 rounded-full hover:bg-[var(--text-color)]/10 text-[var(--text-color)]" aria-label="Kapat"><X size={18} /></button>
          </div>
          <div className="px-4 pt-0 pb-3">
            <div className="rounded-xl px-3 py-1 overflow-hidden" style={{ backgroundColor: autoOverlay(4, 'var(--bg-color)') as string}}> 
              <div className="max-h-[calc(80vh-4rem)] overflow-y-auto grid grid-cols-2">
                {menuItems.map((item: any, index) => (
                  <button key={item.action} onClick={(e) => executeAction(e, item.handler)} disabled={!item.handler} className={`flex items-center w-full px-4 py-3 text-sm text-left rounded-none hover:bg-[var(--text-color)]/5 odd:border-r odd:border-[color-mix(in_srgb,var(--text-color)_10%,transparent)] ${index >= menuItems.length - 2 ? 'border-b-0' : 'border-b border-[color-mix(in_srgb,var(--text-color)_10%,transparent)]'}`} style={{ color: item.isGreen ? 'var(--success-color)' : 'var(--text-color)' }}>
                    <item.icon size={18} className="mr-3 opacity-80 flex-shrink-0" />
                    <span className="flex-grow truncate">{item.text}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
          <div className="pb-safe"></div>
        </div>
      </div>

      {/* ✅ Bookmark Bottom Sheet */}
      <BookmarkBottomSheet
        isOpen={isBookmarkSheetOpen}
        onClose={() => setIsBookmarkSheetOpen(false)}
        onSelectCollection={handleBookmarkSave}
        onCreateCollection={handleCreateCollection}
        collections={collections}
        selectedText={`${surahName || 'Sure'}, ${verseData?.verse_no}. Ayet`}
        // ✅ Quran meal seçimi props'ları
        isQuranVerse={true}
        availableTranslators={availableTranslators}
        selectedTranslators={selectedTranslators}
      />

      {/* ✅ Note Bottom Sheet */}
      <NoteBottomSheet
        isOpen={isNoteSheetOpen}
        onClose={() => setIsNoteSheetOpen(false)}
        selectedText={`${surahName || 'Sure'}, ${verseData?.verse_no}. Ayet`}
        noteContent={noteContent}
        onNoteContentChange={setNoteContent}
        onSave={handleNoteSave}
      />

      {/* ✅ Toast Notification */}
      <AnnotationToast
        isVisible={toastState.isVisible}
        message={toastState.message}
        type={toastState.type}
        onClose={hideToast}
      />
    </>
  );
};