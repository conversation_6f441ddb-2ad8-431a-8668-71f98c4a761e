import { memo, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { MessageSquare, Highlighter, Bookmark, ExternalLink } from 'lucide-react';
import { autoOverlay } from '@shared/hooks/autooverlay';

// Annotation type definition (geçici olarak burada tanımlıyoruz)
interface Annotation {
  id: string;
  annotation_type: 'note' | 'highlight' | 'bookmark';
  selected_text: string;
  annotation_content?: string;
  color?: string;
  book_id: string;
  section_id: string;
  created_at: string;
}

interface SavedContentCardProps {
  /** Annotation verisi */
  annotation: Annotation;
  /** Card'a tıklandığında tetiklenecek fonksiyon */
  onClick?: (annotation: Annotation) => void;
  /** Özel CSS sınıfı */
  className?: string;
  /** Compact görünüm (daha küçük) */
  compact?: boolean;
}

const SavedContentCard = ({
  annotation,
  onClick,
  className = '',
  compact = false
}: SavedContentCardProps) => {
  const cardBgColor = autoOverlay(6, 'var(--bg-color)');
  const borderColor = autoOverlay(15, 'var(--bg-color)');

  // Annotation tipine göre icon, renk ve gradient
  const typeConfig = useMemo(() => {
    switch (annotation.annotation_type) {
      case 'note':
        return {
          icon: MessageSquare,
          color: '#fbbf24',
          label: 'Şerh',
          gradient: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
          lightBg: '#fef3c7',
          darkBg: '#451a03'
        };
      case 'highlight':
        return {
          icon: Highlighter,
          color: annotation.color || '#f59e0b',
          label: 'Vurgu',
          gradient: `linear-gradient(135deg, ${annotation.color || '#f59e0b'} 0%, ${annotation.color || '#d97706'} 100%)`,
          lightBg: annotation.color ? `${annotation.color}15` : '#fef3c7',
          darkBg: annotation.color ? `${annotation.color}20` : '#451a03'
        };
      case 'bookmark':
        return {
          icon: Bookmark,
          color: '#3b82f6',
          label: 'Yer İmi',
          gradient: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
          lightBg: '#dbeafe',
          darkBg: '#1e3a8a'
        };
      default:
        return {
          icon: MessageSquare,
          color: '#6b7280',
          label: 'İçerik',
          gradient: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
          lightBg: '#f3f4f6',
          darkBg: '#374151'
        };
    }
  }, [annotation.annotation_type, annotation.color]);

  // Tarih formatı
  const formattedDate = useMemo(() => {
    const date = new Date(annotation.created_at);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} saat önce`;
    } else if (diffInHours < 24 * 7) {
      return `${Math.floor(diffInHours / 24)} gün önce`;
    } else {
      return date.toLocaleDateString('tr-TR', {
        day: 'numeric',
        month: 'short'
      });
    }
  }, [annotation.created_at]);

  // Metin önizlemesi (compact modda daha kısa)
  const textPreview = useMemo(() => {
    const maxLength = compact ? 80 : 120;
    if (annotation.selected_text.length <= maxLength) {
      return annotation.selected_text;
    }
    return annotation.selected_text.substring(0, maxLength) + '...';
  }, [annotation.selected_text, compact]);

  // ✅ Kitap ve bölüm bilgisi - Quran ayetleri için özel format
  const locationInfo = useMemo(() => {
    // Quran ayetleri için özel format
    if (annotation.book_id === 'quran' && (annotation as any).metadata) {
      const metadata = (annotation as any).metadata;
      if (metadata.surah_name && metadata.verse_number) {
        return `${metadata.surah_name} • ${metadata.verse_number}. Ayet`;
      }
      if (metadata.verse_key) {
        const [surahId, verseNo] = metadata.verse_key.split('-');
        return `Sure ${surahId} • ${verseNo}. Ayet`;
      }
    }

    // Risale ve diğer kitaplar için
    return `Kitap ${annotation.book_id} • Bölüm ${annotation.section_id}`;
  }, [annotation.book_id, annotation.section_id, (annotation as any).metadata]);

  // ✅ Navigation path oluştur - Quran ayetleri için özel yönlendirme
  const navigationPath = useMemo(() => {
    // Quran ayetleri için
    if (annotation.book_id === 'quran') {
      const metadata = (annotation as any).metadata;
      if (metadata?.verse_key) {
        const [surahId] = metadata.verse_key.split('-');
        return `/kuran/${surahId}#verse-${metadata.verse_key}`;
      }
      return `/kuran/${annotation.section_id}`;
    }

    // Risale ve diğer kitaplar için
    return `/risale/${annotation.book_id}/${annotation.section_id}#annotation-${annotation.id}`;
  }, [annotation.book_id, annotation.section_id, annotation.id, (annotation as any).metadata]);

  const IconComponent = typeConfig.icon;

  return (
    <div
      className={`saved-content-card w-full max-w-[400px] flex flex-col ${className}`}
    >
      <div
        className={`w-full rounded-xl border transition-all duration-300 hover:shadow-lg hover:scale-[1.02] cursor-pointer ${
          compact ? 'p-4' : 'p-5'
        }`}
        style={{
          backgroundColor: cardBgColor,
          borderColor: borderColor,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}
        onClick={() => onClick?.(annotation)}
      >
        {/* Header with gradient accent */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <div
              className={`${compact ? 'p-2' : 'p-2.5'} rounded-lg`}
              style={{ background: typeConfig.gradient }}
            >
              <IconComponent
                size={compact ? 16 : 18}
                className="text-white"
              />
            </div>
            <div>
              <span
                className={`font-semibold ${compact ? 'text-sm' : 'text-base'}`}
                style={{ color: 'var(--text-color)' }}
              >
                {typeConfig.label}
              </span>
              <p
                className={`${compact ? 'text-xs' : 'text-sm'} opacity-60 m-0 mt-0.5`}
                style={{ color: 'var(--text-color)' }}
              >
                {formattedDate}
              </p>
            </div>
          </div>
        </div>

        {/* Content Preview */}
        <div className="mb-4">
          <p
            className={`${compact ? 'text-sm' : 'text-base'} leading-relaxed mb-3 font-medium`}
            style={{ color: 'var(--text-color)' }}
          >
            "{textPreview}"
          </p>

          {/* Annotation Content (sadece note tipinde) */}
          {annotation.annotation_type === 'note' && annotation.annotation_content && (
            <div
              className={`${compact ? 'p-3' : 'p-4'} rounded-lg mt-3 border-l-4`}
              style={{
                backgroundColor: autoOverlay(4, 'var(--bg-color)'),
                borderLeftColor: typeConfig.color
              }}
            >
              <p
                className={`${compact ? 'text-sm' : 'text-base'} italic opacity-90 m-0`}
                style={{ color: 'var(--text-color)' }}
              >
                {annotation.annotation_content}
              </p>
            </div>
          )}

          {/* ✅ Quran Meal Bilgileri (bookmark tipinde) */}
          {annotation.book_id === 'quran' && annotation.annotation_type === 'bookmark' && (annotation as any).metadata?.selected_meals && (
            <div
              className={`${compact ? 'p-3' : 'p-4'} rounded-lg mt-3 border-l-4`}
              style={{
                backgroundColor: autoOverlay(4, 'var(--bg-color)'),
                borderLeftColor: typeConfig.color
              }}
            >
              <p className={`${compact ? 'text-xs' : 'text-sm'} font-medium mb-2 opacity-70`} style={{ color: 'var(--text-color)' }}>
                Kaydedilen İçerikler:
              </p>
              <div className="flex flex-wrap gap-1">
                {(annotation as any).metadata.selected_meals.map((meal: any, index: number) => (
                  <span
                    key={index}
                    className={`${compact ? 'text-xs px-2 py-1' : 'text-sm px-2.5 py-1'} rounded-md font-medium`}
                    style={{
                      backgroundColor: `${typeConfig.color}15`,
                      color: typeConfig.color
                    }}
                  >
                    {meal.name}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer with location and actions */}
        <div className="flex items-center justify-between pt-3 border-t" style={{ borderColor: autoOverlay(8, 'var(--bg-color)') }}>
          <span
            className={`${compact ? 'text-xs' : 'text-sm'} opacity-70 font-medium`}
            style={{ color: 'var(--text-color)' }}
          >
            {locationInfo}
          </span>

          {/* Action Button */}
          <Link
            to={navigationPath}
            className={`${compact ? 'px-3 py-1.5' : 'px-4 py-2'} rounded-lg font-medium transition-all duration-200 hover:scale-105`}
            style={{
              backgroundColor: autoOverlay(8, 'var(--bg-color)'),
              color: typeConfig.color
            }}
            title="Konuma Git"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center gap-2">
              <ExternalLink size={compact ? 12 : 14} />
              <span className={compact ? 'text-xs' : 'text-sm'}>Git</span>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default memo(SavedContentCard);
export { SavedContentCard };
