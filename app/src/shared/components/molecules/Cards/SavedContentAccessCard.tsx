import { memo } from 'react';
import { Link } from 'react-router-dom';
import { Bookmark } from 'lucide-react';

interface SavedContentAccessCardProps {
  className?: string;
}

const SavedContentAccessCard = ({
  className = ''
}: SavedContentAccessCardProps) => {
  return (
    <Link
      to="/saved-content"
      className={`relative w-full h-20 rounded-xl cursor-pointer transition-all duration-300 ease-out hover:scale-[1.02] hover:shadow-lg group no-underline block ${className}`}
      style={{
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
      }}
    >
      <div className="absolute inset-0 bg-black/10 rounded-xl group-hover:bg-black/5 transition-colors duration-300"></div>
      <div className="relative h-full flex items-center justify-between px-5">
        <span className="text-white font-medium text-base tracking-wide">
          <PERSON><PERSON><PERSON><PERSON><PERSON>
        </span>
        <Bookmark size={20} className="text-white/80" />
      </div>
    </Link>
  );
};

export default memo(SavedContentAccessCard);
export { SavedContentAccessCard };
