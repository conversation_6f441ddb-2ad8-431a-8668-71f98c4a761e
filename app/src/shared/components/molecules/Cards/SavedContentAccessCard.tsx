import { memo } from 'react';
import { Link } from 'react-router-dom';
import { Bookmark } from 'lucide-react';

interface SavedContentAccessCardProps {
  className?: string;
}

const SavedContentAccessCard = ({
  className = ''
}: SavedContentAccessCardProps) => {
  return (
    <Link
      to="/saved-content"
      className={`relative w-full h-20 rounded-xl cursor-pointer transition-all duration-300 ease-out hover:scale-[1.02] hover:shadow-md group no-underline block ${className}`}
      style={{
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        border: '1px solid color-mix(in srgb, var(--text-color) 15%, transparent)'
      }}
    >
      <div className="relative h-full flex items-center justify-between px-5">
        <span className="font-medium text-base" style={{ color: 'var(--text-color)' }}>
          <PERSON><PERSON><PERSON><PERSON><PERSON>
        </span>
        <Bookmark size={18} style={{ color: 'var(--text-color)' }} className="opacity-60" />
      </div>
    </Link>
  );
};

export default memo(SavedContentAccessCard);
export { SavedContentAccessCard };
